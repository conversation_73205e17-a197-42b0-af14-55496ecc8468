import { TeamOutlined } from '@ant-design/icons';
import {
  Form,
  Input,
  Modal,
  Select,
  Space,
  Typography,
  message,
} from 'antd';
import React from 'react';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface CreateTeamModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess?: () => void;
}

/**
 * 新建团队Modal组件
 *
 * 提供创建新团队的表单界面，包含团队基本信息的输入和验证。
 *
 * 主要功能：
 * 1. 团队名称输入和验证
 * 2. 团队描述输入
 * 3. 团队类型选择
 * 4. 表单提交和错误处理
 *
 * Props:
 * - visible: 控制Modal显示/隐藏
 * - onCancel: 取消操作回调
 * - onSuccess: 创建成功回调
 */
const CreateTeamModal: React.FC<CreateTeamModalProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('创建团队:', values);
      
      // TODO: 调用创建团队的API
      // await TeamService.createTeam(values);
      
      message.success('团队创建成功！');
      form.resetFields();
      onSuccess?.();
      onCancel();
    } catch (error) {
      console.error('创建团队失败:', error);
      message.error('创建团队失败，请稍后重试');
    }
  };

  // 处理取消操作
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={
        <Space align="center">
          <TeamOutlined style={{ fontSize: 18, color: '#1890ff' }} />
          <Title level={4} style={{ margin: 0, fontSize: 16 }}>
            新建团队
          </Title>
        </Space>
      }
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      okText="创建团队"
      cancelText="取消"
      width={520}
      destroyOnClose
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: 16,
        },
        body: {
          padding: '24px',
        },
      }}
    >
      <div style={{ marginBottom: 16 }}>
        <Text style={{ color: '#8c8c8c', fontSize: 14 }}>
          创建一个新的团队来协作管理项目和任务
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        requiredMark={false}
        autoComplete="off"
      >
        {/* 团队名称 */}
        <Form.Item
          label={
            <Text style={{ fontWeight: 600, fontSize: 14 }}>
              团队名称
            </Text>
          }
          name="teamName"
          rules={[
            { required: true, message: '请输入团队名称' },
            { min: 2, message: '团队名称至少2个字符' },
            { max: 50, message: '团队名称不能超过50个字符' },
          ]}
        >
          <Input
            placeholder="请输入团队名称"
            style={{
              borderRadius: 6,
              fontSize: 14,
            }}
          />
        </Form.Item>

        {/* 团队类型 */}
        <Form.Item
          label={
            <Text style={{ fontWeight: 600, fontSize: 14 }}>
              团队类型
            </Text>
          }
          name="teamType"
          rules={[{ required: true, message: '请选择团队类型' }]}
        >
          <Select
            placeholder="请选择团队类型"
            style={{
              borderRadius: 6,
            }}
            options={[
              { value: 'project', label: '项目团队' },
              { value: 'department', label: '部门团队' },
              { value: 'temporary', label: '临时团队' },
              { value: 'other', label: '其他' },
            ]}
          />
        </Form.Item>

        {/* 团队描述 */}
        <Form.Item
          label={
            <Text style={{ fontWeight: 600, fontSize: 14 }}>
              团队描述
            </Text>
          }
          name="description"
          rules={[
            { max: 200, message: '团队描述不能超过200个字符' },
          ]}
        >
          <TextArea
            placeholder="请输入团队描述（可选）"
            rows={4}
            style={{
              borderRadius: 6,
              fontSize: 14,
            }}
            showCount
            maxLength={200}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateTeamModal;
