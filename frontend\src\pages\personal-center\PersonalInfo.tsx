import {
  ClockCircleOutlined,
  MailOutlined,
  PhoneOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Avatar,
  Card,
  Col,
  Flex,
  Row,
  Space,
  Spin,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserProfileDetailResponse } from '@/types/user';

const { Title, Text } = Typography;

/**
 * 个人信息组件
 *
 * 显示用户的完整个人信息，包括基本信息和登录历史。
 * 整合了原UserProfileCard和LastLoginInfo组件的功能。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名、邮箱、电话
 * 3. 显示注册日期
 * 4. 显示最后登录时间和登录团队
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */
const PersonalInfo: React.FC = () => {
  /**
   * 用户详细信息状态管理
   */
  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({
    name: '',
    position: '',
    email: '',
    telephone: '',
    registerDate: '',
    lastLoginTime: '',
    lastLoginTeam: '',
    teamCount: 0,
    avatar: '',
  });

  const [userInfoLoading, setUserInfoLoading] = useState(true);
  const [userInfoError, setUserInfoError] = useState<string | null>(null);

  // 获取用户数据
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userDetail = await UserService.getUserProfileDetail();
        setUserInfo(userDetail);
        setUserInfoError(null);
      } catch (error) {
        console.error('获取用户详细信息失败:', error);
        setUserInfoError('获取用户详细信息失败，请稍后重试');
      } finally {
        setUserInfoLoading(false);
      }
    };

    fetchUserData();
  }, []);

  return (
    <Card
      title="个人信息"
      style={{
        marginBottom: 16,
        borderRadius: 8,
      }}
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: 12,
        },
        body: {
          padding: '16px',
        },
      }}
    >
      {userInfoError ? (
        <Alert
          message="个人信息加载失败"
          description={userInfoError}
          type="error"
          showIcon
        />
      ) : (
        <Spin spinning={userInfoLoading}>
          {/* 明信片式紧凑布局 */}
          <div style={{ padding: '16px' }}>
            <Row gutter={[16, 0]} align="middle">
              {/* 左侧：头像区域 */}
              <Col xs={24} sm={6} md={5} lg={4} xl={4}>
                <Flex justify="center" align="center" style={{ height: '100%' }}>
                  <Avatar
                    size={56}
                    shape="circle"
                    style={{
                      backgroundColor: '#1890ff',
                      fontSize: 20,
                      fontWeight: 600,
                      border: '2px solid #e6f4ff',
                      boxShadow: '0 2px 8px rgba(24, 144, 255, 0.12)',
                    }}
                  >
                    {userInfo.name ? (
                      userInfo.name.charAt(0).toUpperCase()
                    ) : (
                      <UserOutlined />
                    )}
                  </Avatar>
                </Flex>
              </Col>

              {/* 右侧：用户信息区域 */}
              <Col xs={24} sm={18} md={19} lg={20} xl={20}>
                <div style={{ paddingLeft: '12px' }}>
                  {/* 第一行：姓名和职位 */}
                  <div style={{ marginBottom: '8px' }}>
                    <Title
                      level={4}
                      style={{
                        margin: 0,
                        fontSize: 18,
                        fontWeight: 600,
                        color: '#262626',
                        lineHeight: 1.3,
                        display: 'inline-block',
                        marginRight: '12px',
                      }}
                    >
                      {userInfo.name || '加载中...'}
                    </Title>
                    {userInfo.position && (
                      <Text
                        style={{
                          fontSize: 13,
                          color: '#8c8c8c',
                          fontWeight: 500,
                          background: '#f5f5f5',
                          padding: '2px 8px',
                          borderRadius: 4,
                          display: 'inline-block',
                        }}
                      >
                        {userInfo.position}
                      </Text>
                    )}
                  </div>

                  {/* 第二行：联系信息 */}
                  <div style={{ marginBottom: '8px' }}>
                    <Space size={16} wrap>
                      {userInfo.email && (
                        <Space size={6} align="center">
                          <MailOutlined
                            style={{
                              fontSize: 14,
                              color: '#1890ff',
                            }}
                          />
                          <Text
                            style={{
                              color: '#595959',
                              fontSize: 13,
                              fontWeight: 500,
                            }}
                          >
                            {userInfo.email}
                          </Text>
                        </Space>
                      )}
                      {userInfo.telephone && (
                        <Space size={6} align="center">
                          <PhoneOutlined
                            style={{
                              fontSize: 14,
                              color: '#52c41a',
                            }}
                          />
                          <Text
                            style={{
                              color: '#595959',
                              fontSize: 13,
                              fontWeight: 500,
                            }}
                          >
                            {userInfo.telephone}
                          </Text>
                        </Space>
                      )}
                    </Space>
                  </div>

                  {/* 第三行：注册日期和登录信息 */}
                  <div>
                    <Space size={16} wrap>
                      {/* 注册日期 */}
                      {userInfo.registerDate && (
                        <Space size={4} align="center">
                          <Text
                            style={{
                              fontSize: 12,
                              color: '#8c8c8c',
                              fontWeight: 500,
                            }}
                          >
                            📅 注册于 {userInfo.registerDate}
                          </Text>
                        </Space>
                      )}

                      {/* 最后登录时间 */}
                      {userInfo.lastLoginTime && (
                        <Space size={4} align="center">
                          <ClockCircleOutlined
                            style={{
                              fontSize: 12,
                              color: '#1890ff',
                            }}
                          />
                          <Text
                            style={{
                              fontSize: 12,
                              color: '#8c8c8c',
                              fontWeight: 500,
                            }}
                          >
                            最后登录：{userInfo.lastLoginTime}
                          </Text>
                        </Space>
                      )}

                      {/* 最后登录团队 */}
                      {userInfo.lastLoginTeam && (
                        <Space size={4} align="center">
                          <TeamOutlined
                            style={{
                              fontSize: 12,
                              color: '#52c41a',
                            }}
                          />
                          <Text
                            style={{
                              fontSize: 12,
                              color: '#8c8c8c',
                              fontWeight: 500,
                            }}
                          >
                            团队：{userInfo.lastLoginTeam}
                          </Text>
                        </Space>
                      )}
                    </Space>
                  </div>
                </div>
              </Col>
            </Row>
          </div>
        </Spin>
      )}
    </Card>
  );
};

export default PersonalInfo;
