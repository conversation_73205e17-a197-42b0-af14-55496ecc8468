import {
  MailOutlined,
  PhoneOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Avatar,
  Card,
  Flex,
  Space,
  Spin,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserProfileDetailResponse } from '@/types/user';

const { Title, Text } = Typography;

/**
 * 个人信息组件
 *
 * 显示用户的基本个人信息，包括头像、姓名、联系方式和注册日期。
 * 这是从原UserProfileCard组件中提取的个人信息部分。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名
 * 3. 显示邮箱和电话（如果有）
 * 4. 显示注册日期
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */
const PersonalInfo: React.FC = () => {
  /**
   * 用户详细信息状态管理
   */
  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({
    name: '',
    position: '',
    email: '',
    telephone: '',
    registerDate: '',
    lastLoginTime: '',
    lastLoginTeam: '',
    teamCount: 0,
    avatar: '',
  });

  const [userInfoLoading, setUserInfoLoading] = useState(true);
  const [userInfoError, setUserInfoError] = useState<string | null>(null);

  // 获取用户数据
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userDetail = await UserService.getUserProfileDetail();
        setUserInfo(userDetail);
        setUserInfoError(null);
      } catch (error) {
        console.error('获取用户详细信息失败:', error);
        setUserInfoError('获取用户详细信息失败，请稍后重试');
      } finally {
        setUserInfoLoading(false);
      }
    };

    fetchUserData();
  }, []);

  return (
    <Card
      title="个人信息"
      style={{
        marginBottom: 16,
        borderRadius: 8,
      }}
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: 12,
        },
        body: {
          padding: '16px',
        },
      }}
    >
      {userInfoError ? (
        <Alert
          message="个人信息加载失败"
          description={userInfoError}
          type="error"
          showIcon
        />
      ) : (
        <Spin spinning={userInfoLoading}>
          <Flex align="center" style={{ minHeight: '80px' }}>
            {/* 用户头像 */}
            <Avatar
              size={64}
              shape="square"
              style={{
                backgroundColor: '#1890ff',
                marginRight: 16,
                fontSize: 24,
                fontWeight: 600,
                border: '2px solid #f0f0f0',
              }}
            >
              {userInfo.name ? (
                userInfo.name.charAt(0).toUpperCase()
              ) : (
                <UserOutlined />
              )}
            </Avatar>

            {/* 用户信息 */}
            <Space direction="vertical" size={4} style={{ flex: 1 }}>
              <Title
                level={4}
                style={{
                  margin: 0,
                  fontSize: 18,
                  fontWeight: 600,
                }}
              >
                {userInfo.name || '加载中...'}
              </Title>

              {/* 联系信息 */}
              <Space direction="vertical" size={4}>
                {userInfo.email && (
                  <Space size={6} align="center">
                    <MailOutlined
                      style={{
                        fontSize: 14,
                        color: '#8c8c8c',
                      }}
                    />
                    <Text
                      style={{
                        color: '#595959',
                        fontSize: 13,
                      }}
                    >
                      {userInfo.email}
                    </Text>
                  </Space>
                )}
                {userInfo.telephone && (
                  <Space size={6} align="center">
                    <PhoneOutlined
                      style={{
                        fontSize: 14,
                        color: '#8c8c8c',
                      }}
                    />
                    <Text
                      style={{
                        color: '#595959',
                        fontSize: 13,
                      }}
                    >
                      {userInfo.telephone}
                    </Text>
                  </Space>
                )}
              </Space>

              {/* 注册日期 */}
              {userInfo.registerDate && (
                <Text
                  style={{
                    fontSize: 12,
                    color: '#8c8c8c',
                    fontWeight: 500,
                  }}
                >
                  注册于 {userInfo.registerDate}
                </Text>
              )}
            </Space>
          </Flex>
        </Spin>
      )}
    </Card>
  );
};

export default PersonalInfo;
