import {
  ClockCircleOutlined,
  MailOutlined,
  PhoneOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Avatar,
  Card,
  Col,
  Flex,
  Row,
  Space,
  Spin,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserProfileDetailResponse } from '@/types/user';

const { Title, Text } = Typography;

/**
 * 个人信息组件
 *
 * 显示用户的完整个人信息，包括基本信息和登录历史。
 * 整合了原UserProfileCard和LastLoginInfo组件的功能。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名、邮箱、电话
 * 3. 显示注册日期
 * 4. 显示最后登录时间和登录团队
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */
const PersonalInfo: React.FC = () => {
  /**
   * 用户详细信息状态管理
   */
  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({
    name: '',
    position: '',
    email: '',
    telephone: '',
    registerDate: '',
    lastLoginTime: '',
    lastLoginTeam: '',
    teamCount: 0,
    avatar: '',
  });

  const [userInfoLoading, setUserInfoLoading] = useState(true);
  const [userInfoError, setUserInfoError] = useState<string | null>(null);

  // 获取用户数据
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userDetail = await UserService.getUserProfileDetail();
        setUserInfo(userDetail);
        setUserInfoError(null);
      } catch (error) {
        console.error('获取用户详细信息失败:', error);
        setUserInfoError('获取用户详细信息失败，请稍后重试');
      } finally {
        setUserInfoLoading(false);
      }
    };

    fetchUserData();
  }, []);

  return (
    <Card
      title="个人信息"
      style={{
        marginBottom: 16,
        borderRadius: 8,
      }}
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: 12,
        },
        body: {
          padding: '16px',
        },
      }}
    >
      {userInfoError ? (
        <Alert
          message="个人信息加载失败"
          description={userInfoError}
          type="error"
          showIcon
        />
      ) : (
        <Spin spinning={userInfoLoading}>
          <div style={{ padding: '24px' }}>
            {/* 主要信息区域 - 使用网格布局 */}
            <Row gutter={[24, 20]} align="middle">
              {/* 左侧：头像区域 */}
              <Col xs={24} sm={8} md={6} lg={6} xl={6}>
                <Flex justify="center" align="center" style={{ height: '100%' }}>
                  <Avatar
                    size={80}
                    shape="circle"
                    style={{
                      backgroundColor: '#1890ff',
                      fontSize: 28,
                      fontWeight: 700,
                      border: '3px solid #e6f4ff',
                      boxShadow: '0 4px 12px rgba(24, 144, 255, 0.15)',
                    }}
                  >
                    {userInfo.name ? (
                      userInfo.name.charAt(0).toUpperCase()
                    ) : (
                      <UserOutlined />
                    )}
                  </Avatar>
                </Flex>
              </Col>

              {/* 右侧：用户信息区域 */}
              <Col xs={24} sm={16} md={18} lg={18} xl={18}>
                <Space direction="vertical" size={16} style={{ width: '100%' }}>
                  {/* 用户姓名 - 突出显示 */}
                  <div>
                    <Title
                      level={3}
                      style={{
                        margin: 0,
                        fontSize: 24,
                        fontWeight: 700,
                        color: '#262626',
                        lineHeight: 1.2,
                      }}
                    >
                      {userInfo.name || '加载中...'}
                    </Title>
                    {userInfo.position && (
                      <Text
                        style={{
                          fontSize: 14,
                          color: '#8c8c8c',
                          fontWeight: 500,
                          marginTop: 4,
                          display: 'block',
                        }}
                      >
                        {userInfo.position}
                      </Text>
                    )}
                  </div>

                  {/* 联系信息 - 两列布局 */}
                  <Row gutter={[16, 8]}>
                    {userInfo.email && (
                      <Col xs={24} sm={12} md={12} lg={12} xl={12}>
                        <Space size={8} align="center">
                          <MailOutlined
                            style={{
                              fontSize: 16,
                              color: '#1890ff',
                            }}
                          />
                          <Text
                            style={{
                              color: '#595959',
                              fontSize: 14,
                              fontWeight: 500,
                            }}
                          >
                            {userInfo.email}
                          </Text>
                        </Space>
                      </Col>
                    )}
                    {userInfo.telephone && (
                      <Col xs={24} sm={12} md={12} lg={12} xl={12}>
                        <Space size={8} align="center">
                          <PhoneOutlined
                            style={{
                              fontSize: 16,
                              color: '#52c41a',
                            }}
                          />
                          <Text
                            style={{
                              color: '#595959',
                              fontSize: 14,
                              fontWeight: 500,
                            }}
                          >
                            {userInfo.telephone}
                          </Text>
                        </Space>
                      </Col>
                    )}
                  </Row>

                  {/* 注册日期 */}
                  {userInfo.registerDate && (
                    <div
                      style={{
                        padding: '8px 12px',
                        background: '#f6f8fa',
                        borderRadius: 6,
                        border: '1px solid #e1e4e8',
                        display: 'inline-block',
                      }}
                    >
                      <Text
                        style={{
                          fontSize: 13,
                          color: '#6a737d',
                          fontWeight: 500,
                        }}
                      >
                        📅 注册于 {userInfo.registerDate}
                      </Text>
                    </div>
                  )}
                </Space>
              </Col>
            </Row>

            {/* 分隔线 */}
            <div
              style={{
                margin: '24px 0',
                height: '1px',
                background: 'linear-gradient(90deg, transparent, #e8e8e8, transparent)',
              }}
            />

            {/* 登录信息区域 - 卡片式设计 */}
            <div
              style={{
                background: 'linear-gradient(135deg, #f8faff 0%, #f0f5ff 100%)',
                borderRadius: 12,
                border: '1px solid #d9e5ff',
                padding: '20px',
                position: 'relative',
                overflow: 'hidden',
              }}
            >
              {/* 装饰性背景元素 */}
              <div
                style={{
                  position: 'absolute',
                  top: -10,
                  right: -10,
                  width: 40,
                  height: 40,
                  background: 'rgba(24, 144, 255, 0.1)',
                  borderRadius: '50%',
                }}
              />

              <Space direction="vertical" size={12} style={{ width: '100%', position: 'relative', zIndex: 1 }}>
                {/* 区域标题 */}
                <Text
                  style={{
                    fontSize: 16,
                    fontWeight: 600,
                    color: '#1890ff',
                    marginBottom: 8,
                    display: 'block',
                  }}
                >
                  🕒 最近活动
                </Text>

                {/* 登录信息 - 网格布局 */}
                <Row gutter={[16, 12]}>
                  {/* 最后登录时间 */}
                  <Col xs={24} sm={12} md={12} lg={12} xl={12}>
                    <div
                      style={{
                        padding: '12px 16px',
                        background: 'rgba(255, 255, 255, 0.8)',
                        borderRadius: 8,
                        border: '1px solid rgba(24, 144, 255, 0.1)',
                      }}
                    >
                      <Space direction="vertical" size={4} style={{ width: '100%' }}>
                        <Space size={6} align="center">
                          <ClockCircleOutlined
                            style={{
                              fontSize: 14,
                              color: '#1890ff',
                            }}
                          />
                          <Text
                            style={{
                              fontSize: 12,
                              color: '#8c8c8c',
                              fontWeight: 500,
                            }}
                          >
                            最后登录时间
                          </Text>
                        </Space>
                        <Text
                          style={{
                            fontSize: 14,
                            color: '#262626',
                            fontWeight: 600,
                            lineHeight: 1.4,
                          }}
                        >
                          {userInfo.lastLoginTime || '暂无记录'}
                        </Text>
                      </Space>
                    </div>
                  </Col>

                  {/* 最后登录团队 */}
                  <Col xs={24} sm={12} md={12} lg={12} xl={12}>
                    <div
                      style={{
                        padding: '12px 16px',
                        background: 'rgba(255, 255, 255, 0.8)',
                        borderRadius: 8,
                        border: '1px solid rgba(82, 196, 26, 0.1)',
                      }}
                    >
                      <Space direction="vertical" size={4} style={{ width: '100%' }}>
                        <Space size={6} align="center">
                          <TeamOutlined
                            style={{
                              fontSize: 14,
                              color: '#52c41a',
                            }}
                          />
                          <Text
                            style={{
                              fontSize: 12,
                              color: '#8c8c8c',
                              fontWeight: 500,
                            }}
                          >
                            最后登录团队
                          </Text>
                        </Space>
                        <Text
                          style={{
                            fontSize: 14,
                            color: '#262626',
                            fontWeight: 600,
                            lineHeight: 1.4,
                          }}
                        >
                          {userInfo.lastLoginTeam || '暂无记录'}
                        </Text>
                      </Space>
                    </div>
                  </Col>
                </Row>
              </Space>
            </div>
          </div>
        </Spin>
      )}
    </Card>
  );
};

export default PersonalInfo;
