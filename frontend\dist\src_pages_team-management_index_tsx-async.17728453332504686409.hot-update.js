globalThis.makoModuleHotUpdate('src/pages/team-management/index.tsx', {
    modules: {
        "src/pages/team-management/components/TeamMemberManagement.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _dayjs = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/dayjs/dayjs.min.js"));
            var _team = __mako_require__("src/services/team.ts");
            var _invitation = __mako_require__("src/services/invitation.ts");
            var _api = __mako_require__("src/types/api.ts");
            var _InvitationStatus = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/InvitationStatus.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text } = _antd.Typography;
            const TeamMemberManagement = ({ teamDetail, onRefresh })=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [members, setMembers] = (0, _react.useState)([]);
                const [searchText, setSearchText] = (0, _react.useState)('');
                const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
                // 邀请管理相关状态
                const [invitations, setInvitations] = (0, _react.useState)([]);
                const [invitationLoading, setInvitationLoading] = (0, _react.useState)(false);
                const [invitationSearchText, setInvitationSearchText] = (0, _react.useState)('');
                const [statusFilter, setStatusFilter] = (0, _react.useState)('');
                (0, _react.useEffect)(()=>{
                    fetchMembers();
                    fetchInvitations();
                }, []);
                const fetchMembers = async ()=>{
                    try {
                        setLoading(true);
                        const memberList = await _team.TeamService.getCurrentTeamMembers();
                        setMembers(memberList || []);
                    } catch (error) {
                        setMembers([]); // 确保在错误时设置为空数组
                    } finally{
                        setLoading(false);
                    }
                };
                // 获取邀请列表
                const fetchInvitations = async ()=>{
                    try {
                        setInvitationLoading(true);
                        const invitationList = await _invitation.InvitationService.getCurrentTeamInvitations();
                        setInvitations(invitationList || []);
                    } catch (error) {
                        setInvitations([]);
                    } finally{
                        setInvitationLoading(false);
                    }
                };
                // 取消邀请
                const handleCancelInvitation = async (invitationId)=>{
                    try {
                        await _invitation.InvitationService.cancelInvitation(invitationId);
                        fetchInvitations();
                        onRefresh();
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                // 移除单个成员
                const handleRemoveMember = async (member)=>{
                    try {
                        await _team.TeamService.removeMember(member.id);
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                // 批量移除成员
                const handleBatchRemove = async ()=>{
                    try {
                        const memberIds = selectedRowKeys;
                        for (const memberId of memberIds)await _team.TeamService.removeMember(memberId);
                        setSelectedRowKeys([]);
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                // 筛选成员
                const filteredMembers = (members || []).filter((member)=>member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase()));
                // 停用/启用成员
                const handleToggleMemberStatus = async (member, isActive)=>{
                    try {
                        await _team.TeamService.updateMemberStatus(member.id, isActive);
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                // 表格列配置
                const columns = [
                    {
                        title: '邮箱',
                        dataIndex: 'email',
                        key: 'email',
                        render: (email)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: email
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 173,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '状态',
                        dataIndex: 'isActive',
                        key: 'status',
                        width: 100,
                        render: (isActive)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                color: isActive ? 'green' : 'red',
                                children: isActive ? '启用' : '停用'
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 182,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '加入时间',
                        dataIndex: 'assignedAt',
                        key: 'assignedAt',
                        render: (date)=>new Date(date).toLocaleDateString()
                    },
                    {
                        title: '最后访问',
                        dataIndex: 'lastAccessTime',
                        key: 'lastAccessTime',
                        render: (date)=>new Date(date).toLocaleDateString()
                    },
                    {
                        title: '操作',
                        key: 'action',
                        render: (_, record)=>{
                            if (record.isCreator) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "-"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 204,
                                columnNumber: 18
                            }, this);
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    record.isActive ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "text",
                                        size: "small",
                                        onClick: ()=>handleToggleMemberStatus(record, false),
                                        children: "停用"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 210,
                                        columnNumber: 15
                                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "text",
                                        size: "small",
                                        onClick: ()=>handleToggleMemberStatus(record, true),
                                        children: "启用"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 218,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                        title: "确认移除成员",
                                        description: `确定要移除成员 ${record.name} 吗？此操作不可恢复。`,
                                        onConfirm: ()=>handleRemoveMember(record),
                                        okText: "确认",
                                        cancelText: "取消",
                                        okType: "danger",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "text",
                                            danger: true,
                                            size: "small",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 238,
                                                columnNumber: 23
                                            }, void 0),
                                            children: "移除"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 234,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 226,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 208,
                                columnNumber: 11
                            }, this);
                        }
                    }
                ];
                // 行选择配置
                const rowSelection = {
                    selectedRowKeys,
                    onChange: setSelectedRowKeys,
                    getCheckboxProps: (record)=>({
                            disabled: record.isCreator
                        })
                };
                // 邀请表格列定义
                const invitationColumns = [
                    {
                        title: '受邀人员',
                        key: 'invitee',
                        render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                children: record.inviteeEmail
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 264,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '邀请状态',
                        dataIndex: 'status',
                        key: 'status',
                        render: (status, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_InvitationStatus.default, {
                                status: status,
                                isExpired: record.isExpired
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 272,
                                columnNumber: 9
                            }, this),
                        filters: [
                            {
                                text: '待确认',
                                value: _api.InvitationStatus.PENDING
                            },
                            {
                                text: '已接受',
                                value: _api.InvitationStatus.ACCEPTED
                            },
                            {
                                text: '已拒绝',
                                value: _api.InvitationStatus.REJECTED
                            },
                            {
                                text: '已过期',
                                value: _api.InvitationStatus.EXPIRED
                            },
                            {
                                text: '已取消',
                                value: _api.InvitationStatus.CANCELLED
                            }
                        ],
                        onFilter: (value, record)=>record.status === value
                    },
                    {
                        title: '邀请时间',
                        dataIndex: 'invitedAt',
                        key: 'invitedAt',
                        render: (time)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                                children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 291,
                                columnNumber: 9
                            }, this),
                        sorter: (a, b)=>(0, _dayjs.default)(a.invitedAt).unix() - (0, _dayjs.default)(b.invitedAt).unix()
                    },
                    {
                        title: '过期时间',
                        dataIndex: 'expiresAt',
                        key: 'expiresAt',
                        render: (time, record)=>{
                            const isExpired = record.isExpired;
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: isExpired ? 'danger' : 'secondary',
                                    children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 305,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 304,
                                columnNumber: 11
                            }, this);
                        }
                    },
                    {
                        title: '操作',
                        key: 'action',
                        render: (_, record)=>{
                            if (record.status === _api.InvitationStatus.PENDING && !record.isExpired) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                title: "确定要取消这个邀请吗？",
                                onConfirm: ()=>handleCancelInvitation(record.id),
                                okText: "确定",
                                cancelText: "取消",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    size: "small",
                                    danger: true,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 324,
                                        columnNumber: 49
                                    }, void 0),
                                    children: "取消邀请"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 324,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 318,
                                columnNumber: 13
                            }, this);
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "-"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 330,
                                columnNumber: 16
                            }, this);
                        }
                    }
                ];
                // 过滤邀请列表
                const filteredInvitations = invitations.filter((invitation)=>{
                    const matchesSearch = !invitationSearchText || invitation.inviteeEmail.toLowerCase().includes(invitationSearchText.toLowerCase()) || invitation.inviteeName && invitation.inviteeName.toLowerCase().includes(invitationSearchText.toLowerCase());
                    const matchesStatus = !statusFilter || invitation.status === statusFilter;
                    return matchesSearch && matchesStatus;
                });
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            style: {
                                marginBottom: 16
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                size: "large",
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                children: "团队成员"
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 352,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: '24px',
                                                    fontWeight: 'bold',
                                                    color: '#1890ff'
                                                },
                                                children: (members || []).length
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 353,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 351,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                children: "邀请记录"
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 358,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: '24px',
                                                    fontWeight: 'bold',
                                                    color: '#52c41a'
                                                },
                                                children: (invitations || []).length
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 359,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 357,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 350,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 349,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: [
                                16,
                                16
                            ],
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    lg: 12,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 373,
                                                    columnNumber: 17
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    children: "团队成员"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 374,
                                                    columnNumber: 17
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 372,
                                            columnNumber: 15
                                        }, void 0),
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    marginBottom: 16
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    style: {
                                                        width: '100%',
                                                        justifyContent: 'space-between'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                            placeholder: "搜索成员姓名或邮箱",
                                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 383,
                                                                columnNumber: 27
                                                            }, void 0),
                                                            value: searchText,
                                                            onChange: (e)=>setSearchText(e.target.value),
                                                            style: {
                                                                width: '100%',
                                                                maxWidth: 250
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 381,
                                                            columnNumber: 17
                                                        }, this),
                                                        selectedRowKeys.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                                            title: `确定要移除选中的 ${selectedRowKeys.length} 名成员吗？`,
                                                            onConfirm: handleBatchRemove,
                                                            okText: "确定",
                                                            cancelText: "取消",
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                                danger: true,
                                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                    lineNumber: 395,
                                                                    columnNumber: 42
                                                                }, void 0),
                                                                size: "small",
                                                                children: [
                                                                    "批量移除 (",
                                                                    selectedRowKeys.length,
                                                                    ")"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 395,
                                                                columnNumber: 21
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 389,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 380,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 379,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                                                columns: columns,
                                                dataSource: filteredMembers,
                                                rowKey: "id",
                                                loading: loading,
                                                rowSelection: rowSelection,
                                                pagination: {
                                                    showSizeChanger: true,
                                                    showQuickJumper: true,
                                                    showTotal: (total)=>`共 ${total} 名成员`,
                                                    pageSize: 5,
                                                    size: 'small'
                                                },
                                                size: "small"
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 404,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 370,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 369,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    lg: 12,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 427,
                                                    columnNumber: 17
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    children: "邀请记录"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 428,
                                                    columnNumber: 17
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 426,
                                            columnNumber: 15
                                        }, void 0),
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    marginBottom: 16
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                            placeholder: "搜索邮箱",
                                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 437,
                                                                columnNumber: 27
                                                            }, void 0),
                                                            value: invitationSearchText,
                                                            onChange: (e)=>setInvitationSearchText(e.target.value),
                                                            style: {
                                                                width: 200
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 435,
                                                            columnNumber: 17
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                                            placeholder: "筛选状态",
                                                            value: statusFilter,
                                                            onChange: setStatusFilter,
                                                            style: {
                                                                width: 120
                                                            },
                                                            allowClear: true,
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                    value: _api.InvitationStatus.PENDING,
                                                                    children: "待确认"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                    lineNumber: 449,
                                                                    columnNumber: 19
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                    value: _api.InvitationStatus.ACCEPTED,
                                                                    children: "已接受"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                    lineNumber: 450,
                                                                    columnNumber: 19
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                    value: _api.InvitationStatus.REJECTED,
                                                                    children: "已拒绝"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                    lineNumber: 451,
                                                                    columnNumber: 19
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                    value: _api.InvitationStatus.EXPIRED,
                                                                    children: "已过期"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                    lineNumber: 452,
                                                                    columnNumber: 19
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                    value: _api.InvitationStatus.CANCELLED,
                                                                    children: "已取消"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                    lineNumber: 453,
                                                                    columnNumber: 19
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 442,
                                                            columnNumber: 17
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 434,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 433,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                                                columns: invitationColumns,
                                                dataSource: filteredInvitations,
                                                rowKey: "id",
                                                loading: invitationLoading,
                                                pagination: {
                                                    showSizeChanger: true,
                                                    showQuickJumper: true,
                                                    showTotal: (total)=>`共 ${total} 条邀请记录`,
                                                    pageSize: 5,
                                                    size: 'small'
                                                },
                                                size: "small"
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 459,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 424,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 423,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 367,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 347,
                    columnNumber: 5
                }, this);
            };
            _s(TeamMemberManagement, "ibv9zSlh18CONDw4Qd1cIedeMA0=");
            _c = TeamMemberManagement;
            var _default = TeamMemberManagement;
            var _c;
            $RefreshReg$(_c, "TeamMemberManagement");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '17049575194372001402';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/team-management/index.tsx": [
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/services/invitation.ts": [
            "src/services/invitation.ts"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_team-management_index_tsx-async.17728453332504686409.hot-update.js.map