{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.5124279126474611939.hot-update.js", "src/pages/personal-center/PersonalInfo.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='12306336563942753218';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/team-management/index.tsx\":[\"src/pages/team-management/index.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/services/invitation.ts\":[\"src/services/invitation.ts\"]});;\r\n  },\r\n);\r\n", "import {\n  ClockCircleOutlined,\n  MailOutlined,\n  PhoneOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Avatar,\n  Card,\n  Flex,\n  Space,\n  Spin,\n  Typography,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserProfileDetailResponse } from '@/types/user';\n\nconst { Title, Text } = Typography;\n\n/**\n * 个人信息组件\n *\n * 显示用户的完整个人信息，包括基本信息和登录历史。\n * 整合了原UserProfileCard和LastLoginInfo组件的功能。\n *\n * 主要功能：\n * 1. 显示用户头像（使用姓名首字母）\n * 2. 显示用户姓名、邮箱、电话\n * 3. 显示注册日期\n * 4. 显示最后登录时间和登录团队\n *\n * 数据来源：\n * - 用户详细信息：通过UserService.getUserProfileDetail()获取\n */\nconst PersonalInfo: React.FC = () => {\n  /**\n   * 用户详细信息状态管理\n   */\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\n    name: '',\n    position: '',\n    email: '',\n    telephone: '',\n    registerDate: '',\n    lastLoginTime: '',\n    lastLoginTeam: '',\n    teamCount: 0,\n    avatar: '',\n  });\n\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\n\n  // 获取用户数据\n  useEffect(() => {\n    const fetchUserData = async () => {\n      try {\n        const userDetail = await UserService.getUserProfileDetail();\n        setUserInfo(userDetail);\n        setUserInfoError(null);\n      } catch (error) {\n        console.error('获取用户详细信息失败:', error);\n        setUserInfoError('获取用户详细信息失败，请稍后重试');\n      } finally {\n        setUserInfoLoading(false);\n      }\n    };\n\n    fetchUserData();\n  }, []);\n\n  return (\n    <Card\n      title=\"个人信息\"\n      style={{\n        marginBottom: 16,\n        borderRadius: 8,\n      }}\n      styles={{\n        header: {\n          borderBottom: '1px solid #f0f0f0',\n          paddingBottom: 12,\n        },\n        body: {\n          padding: '16px',\n        },\n      }}\n    >\n      {userInfoError ? (\n        <Alert\n          message=\"个人信息加载失败\"\n          description={userInfoError}\n          type=\"error\"\n          showIcon\n        />\n      ) : (\n        <Spin spinning={userInfoLoading}>\n          <div style={{ padding: '24px' }}>\n            {/* 主要信息区域 - 使用网格布局 */}\n            <Row gutter={[24, 20]} align=\"middle\">\n              {/* 左侧：头像区域 */}\n              <Col xs={24} sm={8} md={6} lg={6} xl={6}>\n                <Flex justify=\"center\" align=\"center\" style={{ height: '100%' }}>\n                  <Avatar\n                    size={80}\n                    shape=\"circle\"\n                    style={{\n                      backgroundColor: '#1890ff',\n                      fontSize: 28,\n                      fontWeight: 700,\n                      border: '3px solid #e6f4ff',\n                      boxShadow: '0 4px 12px rgba(24, 144, 255, 0.15)',\n                    }}\n                  >\n                    {userInfo.name ? (\n                      userInfo.name.charAt(0).toUpperCase()\n                    ) : (\n                      <UserOutlined />\n                    )}\n                  </Avatar>\n                </Flex>\n              </Col>\n\n              {/* 右侧：用户信息区域 */}\n              <Col xs={24} sm={16} md={18} lg={18} xl={18}>\n                <Space direction=\"vertical\" size={16} style={{ width: '100%' }}>\n                  {/* 用户姓名 - 突出显示 */}\n                  <div>\n                    <Title\n                      level={3}\n                      style={{\n                        margin: 0,\n                        fontSize: 24,\n                        fontWeight: 700,\n                        color: '#262626',\n                        lineHeight: 1.2,\n                      }}\n                    >\n                      {userInfo.name || '加载中...'}\n                    </Title>\n                    {userInfo.position && (\n                      <Text\n                        style={{\n                          fontSize: 14,\n                          color: '#8c8c8c',\n                          fontWeight: 500,\n                          marginTop: 4,\n                          display: 'block',\n                        }}\n                      >\n                        {userInfo.position}\n                      </Text>\n                    )}\n                  </div>\n\n                  {/* 联系信息 - 两列布局 */}\n                  <Row gutter={[16, 8]}>\n                    {userInfo.email && (\n                      <Col xs={24} sm={12} md={12} lg={12} xl={12}>\n                        <Space size={8} align=\"center\">\n                          <MailOutlined\n                            style={{\n                              fontSize: 16,\n                              color: '#1890ff',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              color: '#595959',\n                              fontSize: 14,\n                              fontWeight: 500,\n                            }}\n                          >\n                            {userInfo.email}\n                          </Text>\n                        </Space>\n                      </Col>\n                    )}\n                    {userInfo.telephone && (\n                      <Col xs={24} sm={12} md={12} lg={12} xl={12}>\n                        <Space size={8} align=\"center\">\n                          <PhoneOutlined\n                            style={{\n                              fontSize: 16,\n                              color: '#52c41a',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              color: '#595959',\n                              fontSize: 14,\n                              fontWeight: 500,\n                            }}\n                          >\n                            {userInfo.telephone}\n                          </Text>\n                        </Space>\n                      </Col>\n                    )}\n                  </Row>\n\n                  {/* 注册日期 */}\n                  {userInfo.registerDate && (\n                    <div\n                      style={{\n                        padding: '8px 12px',\n                        background: '#f6f8fa',\n                        borderRadius: 6,\n                        border: '1px solid #e1e4e8',\n                        display: 'inline-block',\n                      }}\n                    >\n                      <Text\n                        style={{\n                          fontSize: 13,\n                          color: '#6a737d',\n                          fontWeight: 500,\n                        }}\n                      >\n                        📅 注册于 {userInfo.registerDate}\n                      </Text>\n                    </div>\n                  )}\n                </Space>\n              </Col>\n            </Row>\n\n            {/* 分隔线 */}\n            <div\n              style={{\n                margin: '24px 0',\n                height: '1px',\n                background: 'linear-gradient(90deg, transparent, #e8e8e8, transparent)',\n              }}\n            />\n\n            {/* 登录信息区域 - 卡片式设计 */}\n            <div\n              style={{\n                background: 'linear-gradient(135deg, #f8faff 0%, #f0f5ff 100%)',\n                borderRadius: 12,\n                border: '1px solid #d9e5ff',\n                padding: '20px',\n                position: 'relative',\n                overflow: 'hidden',\n              }}\n            >\n              {/* 装饰性背景元素 */}\n              <div\n                style={{\n                  position: 'absolute',\n                  top: -10,\n                  right: -10,\n                  width: 40,\n                  height: 40,\n                  background: 'rgba(24, 144, 255, 0.1)',\n                  borderRadius: '50%',\n                }}\n              />\n\n              <Space direction=\"vertical\" size={12} style={{ width: '100%', position: 'relative', zIndex: 1 }}>\n                {/* 区域标题 */}\n                <Text\n                  style={{\n                    fontSize: 16,\n                    fontWeight: 600,\n                    color: '#1890ff',\n                    marginBottom: 8,\n                    display: 'block',\n                  }}\n                >\n                  🕒 最近活动\n                </Text>\n\n                {/* 登录信息 - 网格布局 */}\n                <Row gutter={[16, 12]}>\n                  {/* 最后登录时间 */}\n                  <Col xs={24} sm={12} md={12} lg={12} xl={12}>\n                    <div\n                      style={{\n                        padding: '12px 16px',\n                        background: 'rgba(255, 255, 255, 0.8)',\n                        borderRadius: 8,\n                        border: '1px solid rgba(24, 144, 255, 0.1)',\n                      }}\n                    >\n                      <Space direction=\"vertical\" size={4} style={{ width: '100%' }}>\n                        <Space size={6} align=\"center\">\n                          <ClockCircleOutlined\n                            style={{\n                              fontSize: 14,\n                              color: '#1890ff',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              fontSize: 12,\n                              color: '#8c8c8c',\n                              fontWeight: 500,\n                            }}\n                          >\n                            最后登录时间\n                          </Text>\n                        </Space>\n                        <Text\n                          style={{\n                            fontSize: 14,\n                            color: '#262626',\n                            fontWeight: 600,\n                            lineHeight: 1.4,\n                          }}\n                        >\n                          {userInfo.lastLoginTime || '暂无记录'}\n                        </Text>\n                      </Space>\n                    </div>\n                  </Col>\n\n                  {/* 最后登录团队 */}\n                  <Col xs={24} sm={12} md={12} lg={12} xl={12}>\n                    <div\n                      style={{\n                        padding: '12px 16px',\n                        background: 'rgba(255, 255, 255, 0.8)',\n                        borderRadius: 8,\n                        border: '1px solid rgba(82, 196, 26, 0.1)',\n                      }}\n                    >\n                      <Space direction=\"vertical\" size={4} style={{ width: '100%' }}>\n                        <Space size={6} align=\"center\">\n                          <TeamOutlined\n                            style={{\n                              fontSize: 14,\n                              color: '#52c41a',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              fontSize: 12,\n                              color: '#8c8c8c',\n                              fontWeight: 500,\n                            }}\n                          >\n                            最后登录团队\n                          </Text>\n                        </Space>\n                        <Text\n                          style={{\n                            fontSize: 14,\n                            color: '#262626',\n                            fontWeight: 600,\n                            lineHeight: 1.4,\n                          }}\n                        >\n                          {userInfo.lastLoginTeam || '暂无记录'}\n                        </Text>\n                      </Space>\n                    </div>\n                  </Col>\n                </Row>\n              </Space>\n            </div>\n          </div>\n        </Spin>\n      )}\n    </Card>\n  );\n};\n\nexport default PersonalInfo;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCiXb;;;2BAAA;;;;;;0CA9WO;yCASA;oFACoC;yCACf;;;;;;;;;;YAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAElC;;;;;;;;;;;;;;CAcC,GACD,MAAM,eAAyB;;gBAC7B;;GAEC,GACD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;oBAClE,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,WAAW;oBACX,QAAQ;gBACV;gBAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;gBACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,gBAAgB;wBACpB,IAAI;4BACF,MAAM,aAAa,MAAM,iBAAW,CAAC,oBAAoB;4BACzD,YAAY;4BACZ,iBAAiB;wBACnB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,eAAe;4BAC7B,iBAAiB;wBACnB,SAAU;4BACR,mBAAmB;wBACrB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE,2BAAC,UAAI;oBACH,OAAM;oBACN,OAAO;wBACL,cAAc;wBACd,cAAc;oBAChB;oBACA,QAAQ;wBACN,QAAQ;4BACN,cAAc;4BACd,eAAe;wBACjB;wBACA,MAAM;4BACJ,SAAS;wBACX;oBACF;8BAEC,8BACC,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,QAAQ;;;;;6CAGV,2BAAC,UAAI;wBAAC,UAAU;kCACd,cAAA,2BAAC;4BAAI,OAAO;gCAAE,SAAS;4BAAO;;8CAE5B,2BAAC;oCAAI,QAAQ;wCAAC;wCAAI;qCAAG;oCAAE,OAAM;;sDAE3B,2BAAC;4CAAI,IAAI;4CAAI,IAAI;4CAAG,IAAI;4CAAG,IAAI;4CAAG,IAAI;sDACpC,cAAA,2BAAC,UAAI;gDAAC,SAAQ;gDAAS,OAAM;gDAAS,OAAO;oDAAE,QAAQ;gDAAO;0DAC5D,cAAA,2BAAC,YAAM;oDACL,MAAM;oDACN,OAAM;oDACN,OAAO;wDACL,iBAAiB;wDACjB,UAAU;wDACV,YAAY;wDACZ,QAAQ;wDACR,WAAW;oDACb;8DAEC,SAAS,IAAI,GACZ,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,mBAEnC,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;sDAOrB,2BAAC;4CAAI,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,IAAI;sDACvC,cAAA,2BAAC,WAAK;gDAAC,WAAU;gDAAW,MAAM;gDAAI,OAAO;oDAAE,OAAO;gDAAO;;kEAE3D,2BAAC;;0EACC,2BAAC;gEACC,OAAO;gEACP,OAAO;oEACL,QAAQ;oEACR,UAAU;oEACV,YAAY;oEACZ,OAAO;oEACP,YAAY;gEACd;0EAEC,SAAS,IAAI,IAAI;;;;;;4DAEnB,SAAS,QAAQ,kBAChB,2BAAC;gEACC,OAAO;oEACL,UAAU;oEACV,OAAO;oEACP,YAAY;oEACZ,WAAW;oEACX,SAAS;gEACX;0EAEC,SAAS,QAAQ;;;;;;;;;;;;kEAMxB,2BAAC;wDAAI,QAAQ;4DAAC;4DAAI;yDAAE;;4DACjB,SAAS,KAAK,kBACb,2BAAC;gEAAI,IAAI;gEAAI,IAAI;gEAAI,IAAI;gEAAI,IAAI;gEAAI,IAAI;0EACvC,cAAA,2BAAC,WAAK;oEAAC,MAAM;oEAAG,OAAM;;sFACpB,2BAAC,mBAAY;4EACX,OAAO;gFACL,UAAU;gFACV,OAAO;4EACT;;;;;;sFAEF,2BAAC;4EACC,OAAO;gFACL,OAAO;gFACP,UAAU;gFACV,YAAY;4EACd;sFAEC,SAAS,KAAK;;;;;;;;;;;;;;;;;4DAKtB,SAAS,SAAS,kBACjB,2BAAC;gEAAI,IAAI;gEAAI,IAAI;gEAAI,IAAI;gEAAI,IAAI;gEAAI,IAAI;0EACvC,cAAA,2BAAC,WAAK;oEAAC,MAAM;oEAAG,OAAM;;sFACpB,2BAAC,oBAAa;4EACZ,OAAO;gFACL,UAAU;gFACV,OAAO;4EACT;;;;;;sFAEF,2BAAC;4EACC,OAAO;gFACL,OAAO;gFACP,UAAU;gFACV,YAAY;4EACd;sFAEC,SAAS,SAAS;;;;;;;;;;;;;;;;;;;;;;;oDAQ5B,SAAS,YAAY,kBACpB,2BAAC;wDACC,OAAO;4DACL,SAAS;4DACT,YAAY;4DACZ,cAAc;4DACd,QAAQ;4DACR,SAAS;wDACX;kEAEA,cAAA,2BAAC;4DACC,OAAO;gEACL,UAAU;gEACV,OAAO;gEACP,YAAY;4DACd;;gEACD;gEACS,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASzC,2BAAC;oCACC,OAAO;wCACL,QAAQ;wCACR,QAAQ;wCACR,YAAY;oCACd;;;;;;8CAIF,2BAAC;oCACC,OAAO;wCACL,YAAY;wCACZ,cAAc;wCACd,QAAQ;wCACR,SAAS;wCACT,UAAU;wCACV,UAAU;oCACZ;;sDAGA,2BAAC;4CACC,OAAO;gDACL,UAAU;gDACV,KAAK;gDACL,OAAO;gDACP,OAAO;gDACP,QAAQ;gDACR,YAAY;gDACZ,cAAc;4CAChB;;;;;;sDAGF,2BAAC,WAAK;4CAAC,WAAU;4CAAW,MAAM;4CAAI,OAAO;gDAAE,OAAO;gDAAQ,UAAU;gDAAY,QAAQ;4CAAE;;8DAE5F,2BAAC;oDACC,OAAO;wDACL,UAAU;wDACV,YAAY;wDACZ,OAAO;wDACP,cAAc;wDACd,SAAS;oDACX;8DACD;;;;;;8DAKD,2BAAC;oDAAI,QAAQ;wDAAC;wDAAI;qDAAG;;sEAEnB,2BAAC;4DAAI,IAAI;4DAAI,IAAI;4DAAI,IAAI;4DAAI,IAAI;4DAAI,IAAI;sEACvC,cAAA,2BAAC;gEACC,OAAO;oEACL,SAAS;oEACT,YAAY;oEACZ,cAAc;oEACd,QAAQ;gEACV;0EAEA,cAAA,2BAAC,WAAK;oEAAC,WAAU;oEAAW,MAAM;oEAAG,OAAO;wEAAE,OAAO;oEAAO;;sFAC1D,2BAAC,WAAK;4EAAC,MAAM;4EAAG,OAAM;;8FACpB,2BAAC,0BAAmB;oFAClB,OAAO;wFACL,UAAU;wFACV,OAAO;oFACT;;;;;;8FAEF,2BAAC;oFACC,OAAO;wFACL,UAAU;wFACV,OAAO;wFACP,YAAY;oFACd;8FACD;;;;;;;;;;;;sFAIH,2BAAC;4EACC,OAAO;gFACL,UAAU;gFACV,OAAO;gFACP,YAAY;gFACZ,YAAY;4EACd;sFAEC,SAAS,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;sEAOnC,2BAAC;4DAAI,IAAI;4DAAI,IAAI;4DAAI,IAAI;4DAAI,IAAI;4DAAI,IAAI;sEACvC,cAAA,2BAAC;gEACC,OAAO;oEACL,SAAS;oEACT,YAAY;oEACZ,cAAc;oEACd,QAAQ;gEACV;0EAEA,cAAA,2BAAC,WAAK;oEAAC,WAAU;oEAAW,MAAM;oEAAG,OAAO;wEAAE,OAAO;oEAAO;;sFAC1D,2BAAC,WAAK;4EAAC,MAAM;4EAAG,OAAM;;8FACpB,2BAAC,mBAAY;oFACX,OAAO;wFACL,UAAU;wFACV,OAAO;oFACT;;;;;;8FAEF,2BAAC;oFACC,OAAO;wFACL,UAAU;wFACV,OAAO;wFACP,YAAY;oFACd;8FACD;;;;;;;;;;;;sFAIH,2BAAC;4EACC,OAAO;gFACL,UAAU;gFACV,OAAO;gFACP,YAAY;gFACZ,YAAY;4EACd;sFAEC,SAAS,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAarD;eA7UM;iBAAA;gBA+UN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDjXD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,8BAA6B;YAAC;SAA6B;IAAA;;AAC/rB"}