globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/PersonalInfo.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            /**
 * 个人信息组件
 *
 * 显示用户的完整个人信息，包括基本信息和登录历史。
 * 整合了原UserProfileCard和LastLoginInfo组件的功能。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名、邮箱、电话
 * 3. 显示注册日期
 * 4. 显示最后登录时间和登录团队
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */ const PersonalInfo = ()=>{
                _s();
                /**
   * 用户详细信息状态管理
   */ const [userInfo, setUserInfo] = (0, _react.useState)({
                    name: '',
                    position: '',
                    email: '',
                    telephone: '',
                    registerDate: '',
                    lastLoginTime: '',
                    lastLoginTeam: '',
                    teamCount: 0,
                    avatar: ''
                });
                const [userInfoLoading, setUserInfoLoading] = (0, _react.useState)(true);
                const [userInfoError, setUserInfoError] = (0, _react.useState)(null);
                // Modal状态管理
                const [createTeamModalVisible, setCreateTeamModalVisible] = (0, _react.useState)(false);
                const [personalSettingsModalVisible, setPersonalSettingsModalVisible] = (0, _react.useState)(false);
                // 获取用户数据
                (0, _react.useEffect)(()=>{
                    const fetchUserData = async ()=>{
                        try {
                            const userDetail = await _user.UserService.getUserProfileDetail();
                            setUserInfo(userDetail);
                            setUserInfoError(null);
                        } catch (error) {
                            console.error('获取用户详细信息失败:', error);
                            setUserInfoError('获取用户详细信息失败，请稍后重试');
                        } finally{
                            setUserInfoLoading(false);
                        }
                    };
                    fetchUserData();
                }, []);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    title: "个人信息",
                    style: {
                        marginBottom: 16,
                        borderRadius: 8
                    },
                    styles: {
                        header: {
                            borderBottom: '1px solid #f0f0f0',
                            paddingBottom: 12
                        },
                        body: {
                            padding: '16px'
                        }
                    },
                    children: userInfoError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "个人信息加载失败",
                        description: userInfoError,
                        type: "error",
                        showIcon: true
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                        lineNumber: 105,
                        columnNumber: 9
                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                        spinning: userInfoLoading,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                padding: '16px'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                gutter: [
                                    16,
                                    0
                                ],
                                align: "middle",
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        xs: 24,
                                        sm: 6,
                                        md: 5,
                                        lg: 4,
                                        xl: 4,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            justify: "center",
                                            align: "center",
                                            style: {
                                                height: '100%'
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                size: 56,
                                                shape: "circle",
                                                style: {
                                                    backgroundColor: '#1890ff',
                                                    fontSize: 20,
                                                    fontWeight: 600,
                                                    border: '2px solid #e6f4ff',
                                                    boxShadow: '0 2px 8px rgba(24, 144, 255, 0.12)'
                                                },
                                                children: userInfo.name ? userInfo.name.charAt(0).toUpperCase() : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 133,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 119,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 118,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                        lineNumber: 117,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        xs: 24,
                                        sm: 18,
                                        md: 19,
                                        lg: 20,
                                        xl: 20,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                paddingLeft: '12px'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        marginBottom: '8px',
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        alignItems: 'center'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                            level: 4,
                                                            style: {
                                                                margin: 0,
                                                                fontSize: 18,
                                                                fontWeight: 600,
                                                                color: '#262626',
                                                                lineHeight: 1.3
                                                            },
                                                            children: userInfo.name || '加载中...'
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 144,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            size: 8,
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                                    type: "primary",
                                                                    size: "small",
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 162,
                                                                        columnNumber: 31
                                                                    }, void 0),
                                                                    onClick: ()=>setCreateTeamModalVisible(true),
                                                                    style: {
                                                                        borderRadius: 6,
                                                                        fontSize: 12,
                                                                        height: 28,
                                                                        padding: '0 12px'
                                                                    },
                                                                    children: "新建团队"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 159,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                                    size: "small",
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 175,
                                                                        columnNumber: 31
                                                                    }, void 0),
                                                                    onClick: ()=>setPersonalSettingsModalVisible(true),
                                                                    style: {
                                                                        borderRadius: 6,
                                                                        fontSize: 12,
                                                                        height: 28,
                                                                        padding: '0 12px',
                                                                        border: '1px solid #d9d9d9'
                                                                    },
                                                                    children: "个人设置"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 173,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 158,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 143,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        marginBottom: '8px'
                                                    },
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                        size: 16,
                                                        wrap: true,
                                                        children: [
                                                            userInfo.email && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                size: 6,
                                                                align: "center",
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                                                        style: {
                                                                            fontSize: 14,
                                                                            color: '#1890ff'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 195,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            color: '#595959',
                                                                            fontSize: 13,
                                                                            fontWeight: 500
                                                                        },
                                                                        children: userInfo.email
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 201,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 194,
                                                                columnNumber: 25
                                                            }, this),
                                                            userInfo.telephone && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                size: 6,
                                                                align: "center",
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {
                                                                        style: {
                                                                            fontSize: 14,
                                                                            color: '#52c41a'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 214,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            color: '#595959',
                                                                            fontSize: 13,
                                                                            fontWeight: 500
                                                                        },
                                                                        children: userInfo.telephone
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 220,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 213,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 192,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 191,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                        size: 16,
                                                        wrap: true,
                                                        children: [
                                                            userInfo.registerDate && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                size: 4,
                                                                align: "center",
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    style: {
                                                                        fontSize: 12,
                                                                        color: '#8c8c8c',
                                                                        fontWeight: 500
                                                                    },
                                                                    children: [
                                                                        "📅 注册于 ",
                                                                        userInfo.registerDate
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 240,
                                                                    columnNumber: 27
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 239,
                                                                columnNumber: 25
                                                            }, this),
                                                            userInfo.lastLoginTime && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                size: 4,
                                                                align: "center",
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#1890ff'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 255,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#8c8c8c',
                                                                            fontWeight: 500
                                                                        },
                                                                        children: [
                                                                            "最后登录：",
                                                                            userInfo.lastLoginTime
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 261,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 254,
                                                                columnNumber: 25
                                                            }, this),
                                                            userInfo.lastLoginTeam && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                size: 4,
                                                                align: "center",
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#52c41a'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 276,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#8c8c8c',
                                                                            fontWeight: 500
                                                                        },
                                                                        children: [
                                                                            "团队：",
                                                                            userInfo.lastLoginTeam
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 282,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 275,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 236,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 235,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 141,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                        lineNumber: 140,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                lineNumber: 115,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 114,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                        lineNumber: 112,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                    lineNumber: 88,
                    columnNumber: 5
                }, this);
            };
            _s(PersonalInfo, "vUaWmTeVKq8g/H74CrfNdt+fdXo=");
            _c = PersonalInfo;
            var _default = PersonalInfo;
            var _c;
            $RefreshReg$(_c, "PersonalInfo");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '15712615307817413930';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/team-management/index.tsx": [
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/services/invitation.ts": [
            "src/services/invitation.ts"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.1320813281564051756.hot-update.js.map