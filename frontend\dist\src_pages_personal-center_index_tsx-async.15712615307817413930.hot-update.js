globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/PersonalInfo.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var _CreateTeamModal = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/CreateTeamModal.tsx"));
            var _PersonalSettingsModal = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/PersonalSettingsModal.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            /**
 * 个人信息组件
 *
 * 显示用户的完整个人信息，包括基本信息和登录历史。
 * 整合了原UserProfileCard和LastLoginInfo组件的功能。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名、邮箱、电话
 * 3. 显示注册日期
 * 4. 显示最后登录时间和登录团队
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */ const PersonalInfo = ()=>{
                _s();
                /**
   * 用户详细信息状态管理
   */ const [userInfo, setUserInfo] = (0, _react.useState)({
                    name: '',
                    position: '',
                    email: '',
                    telephone: '',
                    registerDate: '',
                    lastLoginTime: '',
                    lastLoginTeam: '',
                    teamCount: 0,
                    avatar: ''
                });
                const [userInfoLoading, setUserInfoLoading] = (0, _react.useState)(true);
                const [userInfoError, setUserInfoError] = (0, _react.useState)(null);
                // Modal状态管理
                const [createTeamModalVisible, setCreateTeamModalVisible] = (0, _react.useState)(false);
                const [personalSettingsModalVisible, setPersonalSettingsModalVisible] = (0, _react.useState)(false);
                // 获取用户数据
                (0, _react.useEffect)(()=>{
                    const fetchUserData = async ()=>{
                        try {
                            const userDetail = await _user.UserService.getUserProfileDetail();
                            setUserInfo(userDetail);
                            setUserInfoError(null);
                        } catch (error) {
                            console.error('获取用户详细信息失败:', error);
                            setUserInfoError('获取用户详细信息失败，请稍后重试');
                        } finally{
                            setUserInfoLoading(false);
                        }
                    };
                    fetchUserData();
                }, []);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    title: "个人信息",
                    style: {
                        marginBottom: 16,
                        borderRadius: 8
                    },
                    styles: {
                        header: {
                            borderBottom: '1px solid #f0f0f0',
                            paddingBottom: 12
                        },
                        body: {
                            padding: '16px'
                        }
                    },
                    children: [
                        userInfoError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "个人信息加载失败",
                            description: userInfoError,
                            type: "error",
                            showIcon: true
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 105,
                            columnNumber: 9
                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            spinning: userInfoLoading,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    padding: '16px'
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                    gutter: [
                                        16,
                                        0
                                    ],
                                    align: "middle",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            sm: 6,
                                            md: 5,
                                            lg: 4,
                                            xl: 4,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                justify: "center",
                                                align: "center",
                                                style: {
                                                    height: '100%'
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                    size: 56,
                                                    shape: "circle",
                                                    style: {
                                                        backgroundColor: '#1890ff',
                                                        fontSize: 20,
                                                        fontWeight: 600,
                                                        border: '2px solid #e6f4ff',
                                                        boxShadow: '0 2px 8px rgba(24, 144, 255, 0.12)'
                                                    },
                                                    children: userInfo.name ? userInfo.name.charAt(0).toUpperCase() : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 133,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 119,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 118,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 117,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            sm: 18,
                                            md: 19,
                                            lg: 20,
                                            xl: 20,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    paddingLeft: '12px'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            marginBottom: '8px',
                                                            display: 'flex',
                                                            justifyContent: 'space-between',
                                                            alignItems: 'center'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                                level: 4,
                                                                style: {
                                                                    margin: 0,
                                                                    fontSize: 18,
                                                                    fontWeight: 600,
                                                                    color: '#262626',
                                                                    lineHeight: 1.3
                                                                },
                                                                children: userInfo.name || '加载中...'
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 144,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                size: 8,
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                                        type: "primary",
                                                                        size: "small",
                                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 162,
                                                                            columnNumber: 31
                                                                        }, void 0),
                                                                        onClick: ()=>setCreateTeamModalVisible(true),
                                                                        style: {
                                                                            borderRadius: 6,
                                                                            fontSize: 12,
                                                                            height: 28,
                                                                            padding: '0 12px'
                                                                        },
                                                                        children: "新建团队"
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 159,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                                        size: "small",
                                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 175,
                                                                            columnNumber: 31
                                                                        }, void 0),
                                                                        onClick: ()=>setPersonalSettingsModalVisible(true),
                                                                        style: {
                                                                            borderRadius: 6,
                                                                            fontSize: 12,
                                                                            height: 28,
                                                                            padding: '0 12px',
                                                                            border: '1px solid #d9d9d9'
                                                                        },
                                                                        children: "个人设置"
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 173,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 158,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 143,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            marginBottom: '8px'
                                                        },
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            size: 16,
                                                            wrap: true,
                                                            children: [
                                                                userInfo.email && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                    size: 6,
                                                                    align: "center",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                                                            style: {
                                                                                fontSize: 14,
                                                                                color: '#1890ff'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 195,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                color: '#595959',
                                                                                fontSize: 13,
                                                                                fontWeight: 500
                                                                            },
                                                                            children: userInfo.email
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 201,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 194,
                                                                    columnNumber: 25
                                                                }, this),
                                                                userInfo.telephone && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                    size: 6,
                                                                    align: "center",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {
                                                                            style: {
                                                                                fontSize: 14,
                                                                                color: '#52c41a'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 214,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                color: '#595959',
                                                                                fontSize: 13,
                                                                                fontWeight: 500
                                                                            },
                                                                            children: userInfo.telephone
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 220,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 213,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 192,
                                                            columnNumber: 21
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 191,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            size: 16,
                                                            wrap: true,
                                                            children: [
                                                                userInfo.registerDate && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                    size: 4,
                                                                    align: "center",
                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#8c8c8c',
                                                                            fontWeight: 500
                                                                        },
                                                                        children: [
                                                                            "📅 注册于 ",
                                                                            userInfo.registerDate
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 240,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 239,
                                                                    columnNumber: 25
                                                                }, this),
                                                                userInfo.lastLoginTime && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                    size: 4,
                                                                    align: "center",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: '#1890ff'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 255,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: '#8c8c8c',
                                                                                fontWeight: 500
                                                                            },
                                                                            children: [
                                                                                "最后登录：",
                                                                                userInfo.lastLoginTime
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 261,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 254,
                                                                    columnNumber: 25
                                                                }, this),
                                                                userInfo.lastLoginTeam && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                    size: 4,
                                                                    align: "center",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: '#52c41a'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 276,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: '#8c8c8c',
                                                                                fontWeight: 500
                                                                            },
                                                                            children: [
                                                                                "团队：",
                                                                                userInfo.lastLoginTeam
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 282,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 275,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 236,
                                                            columnNumber: 21
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 235,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 141,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 140,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                    lineNumber: 115,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                lineNumber: 114,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 112,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_CreateTeamModal.default, {
                            visible: createTeamModalVisible,
                            onCancel: ()=>setCreateTeamModalVisible(false),
                            onSuccess: ()=>{
                                // 可以在这里刷新团队列表或其他操作
                                console.log('团队创建成功');
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 303,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_PersonalSettingsModal.default, {
                            visible: personalSettingsModalVisible,
                            onCancel: ()=>setPersonalSettingsModalVisible(false),
                            userInfo: userInfo,
                            onSuccess: ()=>{
                                // 可以在这里刷新用户信息
                                console.log('个人设置更新成功');
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 312,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                    lineNumber: 88,
                    columnNumber: 5
                }, this);
            };
            _s(PersonalInfo, "vUaWmTeVKq8g/H74CrfNdt+fdXo=");
            _c = PersonalInfo;
            var _default = PersonalInfo;
            var _c;
            $RefreshReg$(_c, "PersonalInfo");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/personal-center/CreateTeamModal.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const { TextArea } = _antd.Input;
            /**
 * 新建团队Modal组件
 *
 * 提供创建新团队的表单界面，包含团队基本信息的输入和验证。
 *
 * 主要功能：
 * 1. 团队名称输入和验证
 * 2. 团队描述输入
 * 3. 团队类型选择
 * 4. 表单提交和错误处理
 *
 * Props:
 * - visible: 控制Modal显示/隐藏
 * - onCancel: 取消操作回调
 * - onSuccess: 创建成功回调
 */ const CreateTeamModal = ({ visible, onCancel, onSuccess })=>{
                _s();
                const [form] = _antd.Form.useForm();
                // 处理表单提交
                const handleSubmit = async ()=>{
                    try {
                        const values = await form.validateFields();
                        console.log('创建团队:', values);
                        // TODO: 调用创建团队的API
                        // await TeamService.createTeam(values);
                        _antd.message.success('团队创建成功！');
                        form.resetFields();
                        onSuccess === null || onSuccess === void 0 || onSuccess();
                        onCancel();
                    } catch (error) {
                        console.error('创建团队失败:', error);
                        _antd.message.error('创建团队失败，请稍后重试');
                    }
                };
                // 处理取消操作
                const handleCancel = ()=>{
                    form.resetFields();
                    onCancel();
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        align: "center",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                style: {
                                    fontSize: 18,
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                                lineNumber: 74,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                level: 4,
                                style: {
                                    margin: 0,
                                    fontSize: 16
                                },
                                children: "新建团队"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                                lineNumber: 75,
                                columnNumber: 11
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, void 0),
                    open: visible,
                    onOk: handleSubmit,
                    onCancel: handleCancel,
                    okText: "创建团队",
                    cancelText: "取消",
                    width: 520,
                    destroyOnClose: true,
                    styles: {
                        header: {
                            borderBottom: '1px solid #f0f0f0',
                            paddingBottom: 16
                        },
                        body: {
                            padding: '24px'
                        }
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                style: {
                                    color: '#8c8c8c',
                                    fontSize: 14
                                },
                                children: "创建一个新的团队来协作管理项目和任务"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                                lineNumber: 98,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                            lineNumber: 97,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                            form: form,
                            layout: "vertical",
                            requiredMark: false,
                            autoComplete: "off",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        style: {
                                            fontWeight: 600,
                                            fontSize: 14
                                        },
                                        children: "团队名称"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                                        lineNumber: 112,
                                        columnNumber: 13
                                    }, void 0),
                                    name: "teamName",
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入团队名称'
                                        },
                                        {
                                            min: 2,
                                            message: '团队名称至少2个字符'
                                        },
                                        {
                                            max: 50,
                                            message: '团队名称不能超过50个字符'
                                        }
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "请输入团队名称",
                                        style: {
                                            borderRadius: 6,
                                            fontSize: 14
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                                        lineNumber: 123,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                                    lineNumber: 110,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        style: {
                                            fontWeight: 600,
                                            fontSize: 14
                                        },
                                        children: "团队类型"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                                        lineNumber: 135,
                                        columnNumber: 13
                                    }, void 0),
                                    name: "teamType",
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择团队类型'
                                        }
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                        placeholder: "请选择团队类型",
                                        style: {
                                            borderRadius: 6
                                        },
                                        options: [
                                            {
                                                value: 'project',
                                                label: '项目团队'
                                            },
                                            {
                                                value: 'department',
                                                label: '部门团队'
                                            },
                                            {
                                                value: 'temporary',
                                                label: '临时团队'
                                            },
                                            {
                                                value: 'other',
                                                label: '其他'
                                            }
                                        ]
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                                        lineNumber: 142,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                                    lineNumber: 133,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        style: {
                                            fontWeight: 600,
                                            fontSize: 14
                                        },
                                        children: "团队描述"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                                        lineNumber: 159,
                                        columnNumber: 13
                                    }, void 0),
                                    name: "description",
                                    rules: [
                                        {
                                            max: 200,
                                            message: '团队描述不能超过200个字符'
                                        }
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                        placeholder: "请输入团队描述（可选）",
                                        rows: 4,
                                        style: {
                                            borderRadius: 6,
                                            fontSize: 14
                                        },
                                        showCount: true,
                                        maxLength: 200
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                                        lineNumber: 168,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                                    lineNumber: 157,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                            lineNumber: 103,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/CreateTeamModal.tsx",
                    lineNumber: 71,
                    columnNumber: 5
                }, this);
            };
            _s(CreateTeamModal, "rI7DrJIrFu7YmlGWYiMFTzs8jF0=", false, function() {
                return [
                    _antd.Form.useForm
                ];
            });
            _c = CreateTeamModal;
            var _default = CreateTeamModal;
            var _c;
            $RefreshReg$(_c, "CreateTeamModal");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/personal-center/PersonalSettingsModal.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            /**
 * 个人设置Modal组件
 *
 * 提供编辑个人信息的界面，包含用户基本信息的修改功能。
 *
 * 主要功能：
 * 1. 编辑用户姓名
 * 2. 编辑联系方式（邮箱、电话）
 * 3. 头像上传（预留功能）
 * 4. 表单验证和提交
 *
 * Props:
 * - visible: 控制Modal显示/隐藏
 * - onCancel: 取消操作回调
 * - onSuccess: 保存成功回调
 * - userInfo: 当前用户信息
 */ const PersonalSettingsModal = ({ visible, onCancel, onSuccess, userInfo })=>{
                _s();
                const [form] = _antd.Form.useForm();
                // 当Modal打开时，填充表单数据
                (0, _react.useEffect)(()=>{
                    if (visible && userInfo) form.setFieldsValue({
                        name: userInfo.name,
                        email: userInfo.email,
                        telephone: userInfo.telephone,
                        position: userInfo.position
                    });
                }, [
                    visible,
                    userInfo,
                    form
                ]);
                // 处理表单提交
                const handleSubmit = async ()=>{
                    try {
                        const values = await form.validateFields();
                        console.log('更新个人信息:', values);
                        // TODO: 调用更新用户信息的API
                        // await UserService.updateUserProfile(values);
                        _antd.message.success('个人信息更新成功！');
                        onSuccess === null || onSuccess === void 0 || onSuccess();
                        onCancel();
                    } catch (error) {
                        console.error('更新个人信息失败:', error);
                        _antd.message.error('更新个人信息失败，请稍后重试');
                    }
                };
                // 处理取消操作
                const handleCancel = ()=>{
                    form.resetFields();
                    onCancel();
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        align: "center",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {
                                style: {
                                    fontSize: 18,
                                    color: '#1890ff'
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                lineNumber: 89,
                                columnNumber: 11
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                level: 4,
                                style: {
                                    margin: 0,
                                    fontSize: 16
                                },
                                children: "个人设置"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                lineNumber: 90,
                                columnNumber: 11
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                        lineNumber: 88,
                        columnNumber: 9
                    }, void 0),
                    open: visible,
                    onOk: handleSubmit,
                    onCancel: handleCancel,
                    okText: "保存设置",
                    cancelText: "取消",
                    width: 520,
                    destroyOnClose: true,
                    styles: {
                        header: {
                            borderBottom: '1px solid #f0f0f0',
                            paddingBottom: 16
                        },
                        body: {
                            padding: '24px'
                        }
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                style: {
                                    color: '#8c8c8c',
                                    fontSize: 14
                                },
                                children: "编辑您的个人信息和偏好设置"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                lineNumber: 113,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                            lineNumber: 112,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                            form: form,
                            layout: "vertical",
                            requiredMark: false,
                            autoComplete: "off",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        style: {
                                            fontWeight: 600,
                                            fontSize: 14
                                        },
                                        children: "头像"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                        lineNumber: 127,
                                        columnNumber: 13
                                    }, void 0),
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: 16
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                size: 64,
                                                shape: "circle",
                                                style: {
                                                    backgroundColor: '#1890ff',
                                                    fontSize: 24,
                                                    fontWeight: 600,
                                                    border: '2px solid #e6f4ff'
                                                },
                                                children: userInfo.name ? userInfo.name.charAt(0).toUpperCase() : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                                    lineNumber: 146,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                                lineNumber: 133,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 14,
                                                        color: '#8c8c8c'
                                                    },
                                                    children: "点击上传新头像（功能开发中）"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                                    lineNumber: 150,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                                lineNumber: 149,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                        lineNumber: 132,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                    lineNumber: 125,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        style: {
                                            fontWeight: 600,
                                            fontSize: 14
                                        },
                                        children: "姓名"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                        lineNumber: 160,
                                        columnNumber: 13
                                    }, void 0),
                                    name: "name",
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入姓名'
                                        },
                                        {
                                            min: 2,
                                            message: '姓名至少2个字符'
                                        },
                                        {
                                            max: 20,
                                            message: '姓名不能超过20个字符'
                                        }
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "请输入姓名",
                                        style: {
                                            borderRadius: 6,
                                            fontSize: 14
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                        lineNumber: 171,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                    lineNumber: 158,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        style: {
                                            fontWeight: 600,
                                            fontSize: 14
                                        },
                                        children: "职位"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                        lineNumber: 183,
                                        columnNumber: 13
                                    }, void 0),
                                    name: "position",
                                    rules: [
                                        {
                                            max: 50,
                                            message: '职位不能超过50个字符'
                                        }
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "请输入职位（可选）",
                                        style: {
                                            borderRadius: 6,
                                            fontSize: 14
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                        lineNumber: 192,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                    lineNumber: 181,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        style: {
                                            fontWeight: 600,
                                            fontSize: 14
                                        },
                                        children: "邮箱"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                        lineNumber: 204,
                                        columnNumber: 13
                                    }, void 0),
                                    name: "email",
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入邮箱'
                                        },
                                        {
                                            type: 'email',
                                            message: '请输入有效的邮箱地址'
                                        }
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "请输入邮箱",
                                        style: {
                                            borderRadius: 6,
                                            fontSize: 14
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                        lineNumber: 214,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                    lineNumber: 202,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        style: {
                                            fontWeight: 600,
                                            fontSize: 14
                                        },
                                        children: "电话"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                        lineNumber: 226,
                                        columnNumber: 13
                                    }, void 0),
                                    name: "telephone",
                                    rules: [
                                        {
                                            pattern: /^1[3-9]\d{9}$/,
                                            message: '请输入有效的手机号码'
                                        }
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "请输入电话（可选）",
                                        style: {
                                            borderRadius: 6,
                                            fontSize: 14
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                        lineNumber: 235,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                                    lineNumber: 224,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                            lineNumber: 118,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/PersonalSettingsModal.tsx",
                    lineNumber: 86,
                    columnNumber: 5
                }, this);
            };
            _s(PersonalSettingsModal, "hm6PaQ/QYB/X6ENq1nmkdh1TV0k=", false, function() {
                return [
                    _antd.Form.useForm
                ];
            });
            _c = PersonalSettingsModal;
            var _default = PersonalSettingsModal;
            var _c;
            $RefreshReg$(_c, "PersonalSettingsModal");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '15128533170076447569';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/team-management/index.tsx": [
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/services/invitation.ts": [
            "src/services/invitation.ts"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.15712615307817413930.hot-update.js.map