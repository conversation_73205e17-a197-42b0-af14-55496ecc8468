{"version": 3, "sources": ["src/pages/personal-center/TeamListCard.tsx", "src/pages/personal-center/TodoManagement.tsx", "src/pages/personal-center/UserProfileCard.tsx", "src/pages/personal-center/index.tsx", "src/services/todo.ts", "src/utils/teamSelectionUtils.ts"], "sourcesContent": ["import {\n  CarOutlined,\n  CheckCircleOutlined,\n  ClockCircleOutlined,\n  CrownOutlined,\n  ExclamationCircleOutlined,\n  MinusCircleOutlined,\n  RightOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { history, useModel } from '@umijs/max';\nimport {\n  <PERSON><PERSON>,\n  Button,\n  Card,\n  Col,\n  Flex,\n  List,\n  message,\n  Row,\n  Spin,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { AuthService } from '@/services';\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse } from '@/types/api';\nimport {\n  getTeamIdFromCurrentToken,\n  hasTeamInCurrentToken,\n  getUserIdFromCurrentToken,\n} from '@/utils/tokenUtils';\nimport { recordTeamSelection, hasUserSelectedTeam } from '@/utils/teamSelectionUtils';\n\nconst { Text, Title } = Typography;\n \n\n\n\n// 响应式布局样式\nconst styles = `\n  .team-item .ant-card-body {\n    padding: 0 !important;\n  }\n\n  .team-item:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;\n  }\n\n  @media (max-width: 768px) {\n    .team-item {\n      margin-bottom: 8px;\n    }\n\n    .team-stats-row {\n      margin-top: 8px;\n    }\n\n    .team-info-wrap {\n      gap: 8px !important;\n    }\n  }\n\n  @media (max-width: 576px) {\n    .team-stats-row {\n      margin-top: 12px;\n    }\n\n    .team-stats-col {\n      margin-bottom: 4px;\n    }\n\n    .team-info-wrap {\n      gap: 6px !important;\n    }\n\n    .team-meta-info {\n      flex-wrap: wrap;\n      gap: 6px !important;\n    }\n\n    .team-status-badges {\n      flex-wrap: wrap;\n      gap: 4px !important;\n      margin-top: 4px;\n    }\n  }\n\n  @media (max-width: 480px) {\n    .team-name-text {\n      font-size: 14px !important;\n    }\n\n    .team-meta-text {\n      font-size: 11px !important;\n    }\n\n    .team-meta-info {\n      gap: 4px !important;\n    }\n\n    .team-status-badges {\n      gap: 3px !important;\n    }\n  }\n`;\n\n/**\n * 团队列表卡片组件\n *\n * 这是个人中心页面的核心组件，负责显示用户所属的团队列表，\n * 并提供团队切换、创建团队等功能。是团队管理系统的重要入口。\n *\n * 主要功能：\n * 1. 显示用户所属的所有团队\n * 2. 支持团队切换功能\n * 3. 支持创建新团队\n * 4. 显示当前选择的团队状态\n * 5. 处理团队切换过程中的状态管理\n *\n * 状态管理：\n * - 团队列表数据的获取和显示\n * - 团队切换过程的加载状态\n * - 创建团队模态框的状态\n * - 错误状态的处理和显示\n *\n * 团队切换逻辑：\n * 1. 检查用户登录状态\n * 2. 判断是否为当前团队（避免重复切换）\n * 3. 调用后端API进行团队切换\n * 4. 更新本地Token和全局状态\n * 5. 跳转到团队仪表盘\n *\n * 与全局状态的集成：\n * - 监听用户登录状态变化\n * - 同步团队切换后的状态更新\n * - 处理用户注销时的状态清理\n */\nconst TeamListCard: React.FC = () => {\n  /**\n   * 团队列表相关状态管理\n   *\n   * 这些状态用于管理团队列表的显示和交互：\n   * - teams: 用户所属的团队列表数据\n   * - loading: 团队列表加载状态\n   * - error: 错误信息（如网络错误、权限错误等）\n   * - switchingTeamId: 当前正在切换的团队ID（用于显示加载状态）\n   */\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);\n\n  /**\n   * 创建团队功能已移至设置页面\n   *\n   * 为了更好的用户体验和功能组织，创建团队功能已经移动到\n   * 专门的设置页面中。用户可以通过\"团队设置\"按钮跳转到\n   * 设置页面进行团队创建和管理操作。\n   */\n\n  /**\n   * 全局状态管理\n   *\n   * 从UmiJS全局状态中获取用户和团队信息：\n   * - initialState: 包含当前用户和团队信息的全局状态\n   * - setInitialState: 更新全局状态的函数\n   * - currentTeam: 当前选择的团队信息\n   */\n  const { initialState, setInitialState } = useModel('@@initialState');\n  const currentTeam = initialState?.currentTeam;\n\n  /**\n   * Token信息提取\n   *\n   * 从当前存储的Token中提取关键信息，用于状态判断和权限检查：\n   * - currentTokenTeamId: Token中包含的团队ID\n   * - currentUserId: Token中包含的用户ID\n   * - hasTeamInToken: Token是否包含团队信息\n   *\n   * 这些信息用于：\n   * - 判断当前是否已选择团队\n   * - 确定哪个团队是当前激活的团队\n   * - 记录用户的团队选择历史\n   */\n  const currentTokenTeamId = getTeamIdFromCurrentToken();\n  const currentUserId = getUserIdFromCurrentToken();\n  const hasTeamInToken = hasTeamInCurrentToken();\n\n  // 判断是否有真正的当前团队：\n  // 1. Token中有团队信息（说明用户已经选择过团队）\n  // 2. initialState中有团队信息（说明已经获取过团队详情）\n  // 3. 两者的团队ID一致（确保状态同步）\n  // 4. 用户曾经主动选择过这个团队（区分初始登录和主动选择）\n  const hasRealCurrentTeam = !!(\n    hasTeamInToken &&\n    currentTokenTeamId &&\n    currentTeam &&\n    currentTeam.id === currentTokenTeamId &&\n    currentUserId &&\n    hasUserSelectedTeam(currentUserId, currentTokenTeamId)\n  );\n\n  // 获取实际的当前团队ID：只有在用户真正选择过团队且状态同步时才返回团队ID\n  const actualCurrentTeamId = hasRealCurrentTeam ? currentTokenTeamId : null;\n\n  // 调试日志\n  console.log('TeamListCard 状态调试:', {\n    currentTeam: currentTeam?.id,\n    currentTokenTeamId,\n    currentUserId,\n    hasTeamInToken,\n    hasRealCurrentTeam,\n    actualCurrentTeamId,\n    hasUserSelectedCurrentTeam: currentUserId && currentTokenTeamId ? hasUserSelectedTeam(currentUserId, currentTokenTeamId) : false,\n    initialStateCurrentUser: !!initialState?.currentUser,\n  });\n\n  // 获取团队列表数据\n  useEffect(() => {\n    const fetchTeams = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n        const teamsData = await TeamService.getUserTeamsWithStats();\n        setTeams(teamsData);\n      } catch (error) {\n        console.error('获取团队列表失败:', error);\n        setError('获取团队列表失败');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // 只有在用户已登录时才获取团队列表\n    if (initialState?.currentUser) {\n      fetchTeams();\n    }\n  }, [initialState?.currentUser]);\n\n  // 监听全局状态变化，处理注销等情况\n  useEffect(() => {\n    // 如果用户已注销（currentUser为undefined），清除本地团队列表状态\n    if (!initialState?.currentUser) {\n      setTeams([]);\n      setError(null);\n      setLoading(false);\n      setSwitchingTeamId(null);\n    }\n  }, [initialState?.currentUser]);\n\n  // 监听当前团队状态变化\n  useEffect(() => {\n    console.log('当前团队状态变化:', {\n      currentTeam: currentTeam?.id,\n      actualCurrentTeamId,\n      hasRealCurrentTeam,\n    });\n  }, [currentTeam?.id, actualCurrentTeamId, hasRealCurrentTeam]);\n\n  // 创建团队功能已移至设置页面，此处不再需要处理函数\n\n  /**\n   * 团队切换处理函数\n   *\n   * 这是团队切换功能的核心函数，处理用户从一个团队切换到另一个团队的完整流程。\n   * 包括权限检查、API调用、状态更新、页面跳转等步骤。\n   *\n   * 切换流程：\n   * 1. 用户登录状态检查\n   * 2. 当前团队状态判断（避免重复切换）\n   * 3. 调用后端团队选择API\n   * 4. 验证切换结果\n   * 5. 更新本地Token和全局状态\n   * 6. 记录用户选择历史\n   * 7. 跳转到团队仪表盘\n   *\n   * 状态管理：\n   * - 设置切换加载状态（防止重复点击）\n   * - 更新全局用户和团队状态\n   * - 处理切换过程中的错误状态\n   *\n   * 错误处理：\n   * - 网络错误：显示网络连接提示\n   * - 权限错误：由响应拦截器统一处理\n   * - 业务错误：显示具体的错误信息\n   *\n   * @param teamId 要切换到的团队ID\n   * @param teamName 团队名称（用于显示消息）\n   */\n  const handleTeamSwitch = async (teamId: number, teamName: string) => {\n    /**\n     * 用户登录状态检查\n     *\n     * 确保用户已登录才能进行团队切换操作。\n     * 虽然组件层面已有登录检查，但这里再次确认以确保安全性。\n     */\n    if (!initialState?.currentUser) {\n      return;\n    }\n\n    try {\n      /**\n       * 设置切换状态\n       *\n       * 标记当前正在切换的团队ID，用于：\n       * 1. 在UI上显示加载状态\n       * 2. 防止用户重复点击\n       * 3. 提供视觉反馈\n       */\n      setSwitchingTeamId(teamId);\n\n      /**\n       * 当前团队检查\n       *\n       * 如果用户点击的是当前已选择的团队，直接跳转到仪表盘，\n       * 避免不必要的API调用和Token更新。\n       */\n      if (teamId === actualCurrentTeamId) {\n        history.push('/dashboard');\n        return;\n      }\n\n      /**\n       * 执行团队切换API调用\n       *\n       * 调用后端的团队选择接口，后端会：\n       * 1. 验证用户是否有权限访问该团队\n       * 2. 生成包含新团队信息的JWT Token\n       * 3. 返回团队详细信息和切换状态\n       */\n      const response = await AuthService.selectTeam({ teamId });\n\n      /**\n       * 验证切换结果\n       *\n       * 检查后端返回的响应是否表示切换成功：\n       * - teamSelectionSuccess: 切换成功标识\n       * - team: 新团队的详细信息\n       * - team.id: 确认返回的团队ID与请求的一致\n       */\n      if (\n        response.teamSelectionSuccess &&\n        response.team &&\n        response.team.id === teamId\n      ) {\n        /**\n         * 记录用户选择历史\n         *\n         * 将用户的团队选择记录到本地存储，用于：\n         * - 下次登录时的默认团队选择\n         * - 用户行为分析\n         * - 提升用户体验\n         */\n        if (currentUserId) {\n          recordTeamSelection(currentUserId, teamId);\n        }\n\n        /**\n         * 异步更新全局状态\n         *\n         * 由于Token已经更新，需要同步更新全局状态中的用户和团队信息。\n         * 使用异步更新避免阻塞页面跳转，提升用户体验。\n         *\n         * 更新流程：\n         * 1. 并行获取最新的用户信息和团队信息\n         * 2. 验证获取的团队信息是否正确\n         * 3. 更新全局状态\n         * 4. 处理更新过程中的错误\n         */\n        if (\n          initialState?.fetchTeamInfo &&\n          initialState?.fetchUserInfo &&\n          setInitialState\n        ) {\n          // 异步更新状态，不阻塞跳转\n          Promise.all([\n            initialState.fetchUserInfo(),\n            initialState.fetchTeamInfo(),\n          ])\n            .then(([currentUser, currentTeam]) => {\n              // 确认获取的团队信息与切换的团队一致\n              if (currentTeam && currentTeam.id === teamId) {\n                setInitialState({\n                  ...initialState,\n                  currentUser,\n                  currentTeam,\n                });\n              }\n            })\n            .catch((error) => {\n              console.error('更新 initialState 失败:', error);\n              // 状态更新失败不影响团队切换的核心功能\n            });\n        }\n\n        /**\n         * 页面跳转\n         *\n         * 切换成功后跳转到团队仪表盘。\n         * 路由守卫会验证新的Token并允许访问团队页面。\n         */\n        history.push('/dashboard');\n      } else {\n        /**\n         * 切换失败处理\n         *\n         * 如果后端返回的响应不符合预期，说明切换失败。\n         * 记录错误日志并提示用户重试。\n         */\n        // 团队切换响应异常，未返回正确的团队信息\n      }\n    } catch (error: any) {\n      /**\n       * 异常处理\n       *\n       * 处理团队切换过程中可能出现的各种异常：\n       * - 网络错误：连接超时、服务器不可达等\n       * - 权限错误：用户无权限访问该团队\n       * - 业务错误：团队不存在、状态异常等\n       *\n       * 错误处理策略：\n       * 1. 记录详细的错误日志用于调试\n       * 2. 响应拦截器已处理大部分错误消息\n       * 3. 只对网络错误显示通用提示\n       */\n      // 错误处理由响应拦截器统一处理\n    } finally {\n      /**\n       * 清理切换状态\n       *\n       * 无论切换成功还是失败，都要清除切换状态，\n       * 恢复UI的正常状态，允许用户进行下一次操作。\n       */\n      setSwitchingTeamId(null);\n    }\n  };\n\n  return (\n    <>\n      {/* 注入样式 */}\n      <style dangerouslySetInnerHTML={{ __html: styles }} />\n\n      <Card\n        className=\"dashboard-card\"\n        style={{\n          borderRadius: 16,\n          boxShadow: '0 6px 20px rgba(0,0,0,0.08)',\n          border: 'none',\n          background: 'linear-gradient(145deg, #ffffff, #f8faff)',\n        }}\n        title={\n          <Title\n            level={4}\n            style={{\n              margin: 0,\n              background: 'linear-gradient(135deg, #1890ff, #722ed1)',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              fontWeight: 600,\n            }}\n          >\n            团队列表\n          </Title>\n        }\n      >\n        {error ? (\n          <Alert\n            message=\"团队列表加载失败\"\n            description={error}\n            type=\"error\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n        ) : (\n          <Spin spinning={loading}>\n            {!initialState?.currentUser ? (\n              <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n                <Text type=\"secondary\">请先登录以查看团队列表</Text>\n              </div>\n            ) : teams.length === 0 && !loading ? (\n              <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n                <Text type=\"secondary\">暂无团队，请先加入或创建团队</Text>\n              </div>\n            ) : (\n              <List\n                dataSource={teams}\n                renderItem={(item) => (\n                  <List.Item>\n                    <Card\n                      className=\"team-item\"\n                      style={{\n                        background:\n                          actualCurrentTeamId === item.id\n                            ? 'linear-gradient(135deg, #f0f9ff, #e6f4ff)'\n                            : '#fff',\n                        borderRadius: 8,\n                        boxShadow:\n                          actualCurrentTeamId === item.id\n                            ? '0 2px 8px rgba(24, 144, 255, 0.12)'\n                            : '0 1px 4px rgba(0,0,0,0.06)',\n                        width: '100%',\n                        borderLeft: `3px solid ${item.isCreator ? '#722ed1' : '#52c41a'}`,\n                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                        border:\n                          actualCurrentTeamId === item.id\n                            ? '1px solid #91caff'\n                            : '1px solid #f0f0f0',\n                        padding: '12px 16px',\n                        position: 'relative',\n                        overflow: 'hidden',\n                      }}\n                      hoverable\n                      onMouseEnter={(e) => {\n                        if (actualCurrentTeamId !== item.id) {\n                          e.currentTarget.style.transform = 'translateY(-2px)';\n                          e.currentTarget.style.boxShadow =\n                            '0 8px 24px rgba(0,0,0,0.12)';\n                        }\n                      }}\n                      onMouseLeave={(e) => {\n                        if (actualCurrentTeamId !== item.id) {\n                          e.currentTarget.style.transform = 'translateY(0)';\n                          e.currentTarget.style.boxShadow =\n                            '0 2px 8px rgba(0,0,0,0.06)';\n                        }\n                      }}\n                    >\n                      {/* 响应式布局 */}\n                      <Row\n                        gutter={[8, 8]}\n                        align=\"middle\"\n                        style={{ width: '100%' }}\n                      >\n                        {/* 左侧：团队信息 */}\n                        <Col xs={24} sm={24} md={14} lg={12} xl={14}>\n                          <Flex vertical gap={6} className=\"team-info-wrap\">\n                            {/* 团队名称行 */}\n                            <Flex align=\"center\" gap={8} wrap=\"wrap\">\n                              <div\n                                style={{\n                                  cursor: 'pointer',\n                                  padding: '2px 4px',\n                                  borderRadius: 4,\n                                  transition: 'all 0.2s ease',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 6,\n                                }}\n                                onClick={() =>\n                                  handleTeamSwitch(item.id, item.name)\n                                }\n                                onMouseEnter={(e) => {\n                                  e.currentTarget.style.background =\n                                    'rgba(24, 144, 255, 0.05)';\n                                }}\n                                onMouseLeave={(e) => {\n                                  e.currentTarget.style.background =\n                                    'transparent';\n                                }}\n                              >\n                                <Text\n                                  strong\n                                  style={{\n                                    fontSize: 16,\n                                    color:\n                                      actualCurrentTeamId === item.id\n                                        ? '#1890ff'\n                                        : '#262626',\n                                    lineHeight: 1.2,\n                                  }}\n                                >\n                                  {item.name}\n                                </Text>\n                                <RightOutlined\n                                  style={{\n                                    fontSize: 10,\n                                    color:\n                                      actualCurrentTeamId === item.id\n                                        ? '#1890ff'\n                                        : '#8c8c8c',\n                                    verticalAlign: 'middle',\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                  }}\n                                />\n                              </div>\n\n                              {/* 状态标识 */}\n                              {actualCurrentTeamId === item.id && (\n                                <span\n                                  style={{\n                                    background: '#1890ff',\n                                    color: 'white',\n                                    padding: '1px 6px',\n                                    borderRadius: 8,\n                                    fontSize: 10,\n                                    fontWeight: 500,\n                                  }}\n                                >\n                                  当前\n                                </span>\n                              )}\n\n\n\n                              {switchingTeamId === item.id && (\n                                <Flex align=\"center\" gap={4}>\n                                  <Spin size=\"small\" />\n                                  <Text style={{ fontSize: 10, color: '#666' }}>\n                                    切换中\n                                  </Text>\n                                </Flex>\n                              )}\n                            </Flex>\n\n                            {/* 团队基本信息 */}\n                            <Flex align=\"center\" gap={12} wrap=\"wrap\" className=\"team-meta-info\">\n                              <Tooltip\n                                title={`团队创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}\n                              >\n                                <Flex align=\"center\" gap={4}>\n                                  <ClockCircleOutlined\n                                    style={{ color: '#8c8c8c', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    style={{ fontSize: 12, color: '#8c8c8c' }}\n                                  >\n                                    创建: {new Date(\n                                      item.createdAt,\n                                    ).toLocaleDateString('zh-CN')}\n                                  </Text>\n                                </Flex>\n                              </Tooltip>\n\n                              {/* 加入日期 */}\n                              {item.assignedAt && (\n                                <Tooltip\n                                  title={`加入团队时间: ${new Date(item.assignedAt).toLocaleString('zh-CN')}`}\n                                >\n                                  <Flex align=\"center\" gap={4}>\n                                    <UserOutlined\n                                      style={{ color: '#8c8c8c', fontSize: 12 }}\n                                    />\n                                    <Text\n                                      style={{ fontSize: 12, color: '#8c8c8c' }}\n                                    >\n                                      加入: {new Date(\n                                        item.assignedAt,\n                                      ).toLocaleDateString('zh-CN')}\n                                    </Text>\n                                  </Flex>\n                                </Tooltip>\n                              )}\n\n                              <Tooltip\n                                title={`团队成员: ${item.memberCount}人`}\n                              >\n                                <Flex align=\"center\" gap={4}>\n                                  <TeamOutlined\n                                    style={{ color: '#8c8c8c', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    style={{ fontSize: 12, color: '#8c8c8c' }}\n                                  >\n                                    {item.memberCount} 人\n                                  </Text>\n                                </Flex>\n                              </Tooltip>\n                            </Flex>\n\n                            {/* 状态标识行 */}\n                            <Flex align=\"center\" gap={8} wrap=\"wrap\" className=\"team-status-badges\">\n                              {/* 角色标识 */}\n                              <span\n                                style={{\n                                  background: item.isCreator\n                                    ? '#722ed1'\n                                    : '#52c41a',\n                                  color: 'white',\n                                  padding: '2px 6px',\n                                  borderRadius: 8,\n                                  fontSize: 10,\n                                  fontWeight: 500,\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 2,\n                                }}\n                              >\n                                {item.isCreator ? (\n                                  <>\n                                    <CrownOutlined style={{ fontSize: 9 }} />\n                                    管理员\n                                  </>\n                                ) : (\n                                  <>\n                                    <UserOutlined style={{ fontSize: 9 }} />\n                                    成员\n                                  </>\n                                )}\n                              </span>\n\n                              {/* 用户状态标识 */}\n                              <span\n                                style={{\n                                  background: item.isActive ? '#52c41a' : '#ff4d4f',\n                                  color: 'white',\n                                  padding: '2px 6px',\n                                  borderRadius: 8,\n                                  fontSize: 10,\n                                  fontWeight: 500,\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 2,\n                                }}\n                              >\n                                {item.isActive ? (\n                                  <>\n                                    <CheckCircleOutlined style={{ fontSize: 9 }} />\n                                    启用\n                                  </>\n                                ) : (\n                                  <>\n                                    <MinusCircleOutlined style={{ fontSize: 9 }} />\n                                    停用\n                                  </>\n                                )}\n                              </span>\n                            </Flex>\n                          </Flex>\n                        </Col>\n\n                        {/* 右侧：响应式指标卡片 */}\n                        <Col xs={24} sm={24} md={10} lg={12} xl={10}>\n                          <Row\n                            gutter={[4, 4]}\n                            justify={{ xs: 'start', md: 'end' }}\n                          >\n                            {/* 车辆资源 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#f0f7ff',\n                                  border: '1px solid #d9e8ff',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <CarOutlined\n                                    style={{ color: '#1890ff', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#1890ff',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.vehicles || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    车辆\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 人员资源 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#f6ffed',\n                                  border: '1px solid #d1f0be',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <UserOutlined\n                                    style={{ color: '#52c41a', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#52c41a',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.personnel || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    人员\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 临期事项 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#fff7e6',\n                                  border: '1px solid #ffd666',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <ExclamationCircleOutlined\n                                    style={{ color: '#faad14', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#faad14',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.expiring || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    临期\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 逾期事项 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#fff1f0',\n                                  border: '1px solid #ffccc7',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <ExclamationCircleOutlined\n                                    style={{ color: '#ff4d4f', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#ff4d4f',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.overdue || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    逾期\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n                          </Row>\n                        </Col>\n                      </Row>\n                    </Card>\n                  </List.Item>\n                )}\n              />\n            )}\n          </Spin>\n        )}\n      </Card>\n\n      {/* 创建团队功能已移至设置页面 */}\n    </>\n  );\n};\n\nexport default TeamListCard;\n", "import {\n  CalendarOutlined,\n  CheckOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  MoreOutlined,\n  PlusOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport {\n  <PERSON><PERSON>,\n  Button,\n  Card,\n  Col,\n  Dropdown,\n  Flex,\n  Form,\n  Input,\n  List,\n  Modal,\n\n  Progress,\n  Row,\n  Select,\n  Space,\n  Spin,\n  Tabs,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { TodoService } from '@/services/todo';\nimport type { TodoResponse, TodoStatsResponse } from '@/types/api';\n\nconst { Text } = Typography;\nconst { TabPane } = Tabs;\n\n// 使用API类型定义，不需要重复定义接口\ninterface TodoManagementProps {\n  onAddTodo?: (todo: TodoResponse) => void;\n  onUpdateTodo?: (id: number, updatedTodo: Partial<TodoResponse>) => void;\n  onDeleteTodo?: (id: number) => void;\n}\n\nconst TodoManagement: React.FC<TodoManagementProps> = () => {\n  // TODO数据状态管理\n  const [personalTasks, setPersonalTasks] = useState<TodoResponse[]>([]);\n  const [todoStats, setTodoStats] = useState<TodoStatsResponse>({\n    highPriorityCount: 0,\n    mediumPriorityCount: 0,\n    lowPriorityCount: 0,\n    totalCount: 0,\n    completedCount: 0,\n    completionPercentage: 0,\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // 待办事项状态管理\n  const [todoModalVisible, setTodoModalVisible] = useState(false);\n  const [todoForm] = Form.useForm();\n  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);\n\n  // 过滤器状态\n  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'completed'>(\n    'pending',\n  );\n  const [searchText, setSearchText] = useState('');\n\n  // 获取TODO数据\n  useEffect(() => {\n    const fetchTodoData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        console.log('TodoManagement: 开始获取TODO数据');\n\n        // 分别获取TODO列表和统计数据，避免一个失败影响另一个\n        const todosPromise = TodoService.getUserTodos().catch((error) => {\n          console.error('获取TODO列表失败:', error);\n          return [];\n        });\n\n        const statsPromise = TodoService.getTodoStats().catch((error) => {\n          console.error('获取TODO统计失败:', error);\n          return {\n            highPriorityCount: 0,\n            mediumPriorityCount: 0,\n            lowPriorityCount: 0,\n            totalCount: 0,\n            completedCount: 0,\n            completionPercentage: 0,\n          };\n        });\n\n        const [todos, stats] = await Promise.all([todosPromise, statsPromise]);\n\n        console.log('TodoManagement: 获取到TODO列表:', todos);\n        console.log('TodoManagement: 获取到统计数据:', stats);\n\n        setPersonalTasks(todos);\n        setTodoStats(stats);\n      } catch (error) {\n        console.error('获取TODO数据时发生未知错误:', error);\n        setError('获取TODO数据失败，请刷新页面重试');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTodoData();\n  }, []);\n\n  // 根据激活的标签和搜索文本过滤任务\n  const filteredPersonalTasks = (personalTasks || []).filter((task) => {\n    // 根据标签过滤\n    if (activeTab === 'pending' && task.status === 1) return false;\n    if (activeTab === 'completed' && task.status === 0) return false;\n\n    // 根据搜索文本过滤\n    if (\n      searchText &&\n      !task.title.toLowerCase().includes(searchText.toLowerCase())\n    ) {\n      return false;\n    }\n\n    return true;\n  });\n\n  // 处理待办事项操作\n  const handleToggleTodoStatus = async (id: number) => {\n    try {\n      const task = personalTasks.find((t) => t.id === id);\n      if (!task) {\n        return;\n      }\n\n      const newStatus = task.status === 0 ? 1 : 0;\n\n      await TodoService.updateTodo(id, { status: newStatus });\n\n      // 更新本地状态\n      setPersonalTasks(\n        personalTasks.map((task) =>\n          task.id === id ? { ...task, status: newStatus } : task,\n        ),\n      );\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  const handleAddOrUpdateTodo = async (values: any) => {\n    try {\n      if (editingTodoId) {\n        // 更新现有待办事项\n        const updatedTodo = await TodoService.updateTodo(editingTodoId, {\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks(\n          personalTasks.map((task) =>\n            task.id === editingTodoId ? updatedTodo : task,\n          ),\n        );\n      } else {\n        // 添加新待办事项\n        const newTodo = await TodoService.createTodo({\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks([newTodo, ...personalTasks]);\n      }\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n\n      // 重置表单并关闭模态框\n      setTodoModalVisible(false);\n      setEditingTodoId(null);\n      todoForm.resetFields();\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  const handleDeleteTodo = async (id: number) => {\n    try {\n      await TodoService.deleteTodo(id);\n      setPersonalTasks(personalTasks.filter((task) => task.id !== id));\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  return (\n    <Card\n      className=\"dashboard-card\"\n      style={{\n        borderRadius: 12,\n        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',\n        border: 'none',\n        background: 'linear-gradient(145deg, #ffffff, #f5f8ff)',\n      }}\n      title={\n        <Flex justify=\"space-between\" align=\"center\">\n          <Text strong>待办事项</Text>\n        </Flex>\n      }\n    >\n      {/* 响应式标题行：搜索框、新增按钮、优先级计数和完成率 */}\n      <div\n        style={{\n          marginBottom: 16,\n          padding: '12px 16px',\n          background: '#fafbfc',\n          borderRadius: 8,\n          border: '1px solid #f0f0f0',\n        }}\n      >\n        {/* 使用 Row/Col 实现三列响应式布局 */}\n        <Row gutter={[16, 12]} align=\"middle\">\n          {/* 第一列：搜索框和新增按钮 */}\n          <Col xs={24} sm={24} md={8} lg={8} xl={8}>\n            <Flex align=\"center\" gap={12} style={{ width: '100%' }}>\n              <Input.Search\n                placeholder=\"搜索任务...\"\n                allowClear\n                prefix={<SearchOutlined />}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                style={{ flex: 1 }}\n                size=\"middle\"\n              />\n\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => {\n                  setEditingTodoId(null);\n                  todoForm.resetFields();\n                  setTodoModalVisible(true);\n                }}\n                style={{\n                  background: '#1890ff',\n                  borderColor: '#1890ff',\n                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                  fontWeight: 500,\n                  minWidth: 80,\n                }}\n                size=\"middle\"\n              >\n                新增\n              </Button>\n            </Flex>\n          </Col>\n\n          {/* 第二列：各优先级数量 */}\n          <Col xs={24} sm={24} md={8} lg={8} xl={8}>\n            <Flex align=\"center\" justify=\"center\" wrap=\"wrap\">\n              <Space size={12} wrap>\n                <Tooltip\n                  title={`高优先级任务: ${todoStats.highPriorityCount}个`}\n                >\n                  <Flex align=\"center\" gap={4}>\n                    <div\n                      style={{\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        background: '#ff4d4f',\n                      }}\n                    />\n                    <Text\n                      style={{\n                        fontSize: 12,\n                        fontWeight: 500,\n                        color: '#262626',\n                      }}\n                    >\n                      高: {todoStats.highPriorityCount}\n                    </Text>\n                  </Flex>\n                </Tooltip>\n\n                <Tooltip\n                  title={`中优先级任务: ${todoStats.mediumPriorityCount}个`}\n                >\n                  <Flex align=\"center\" gap={4}>\n                    <div\n                      style={{\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        background: '#faad14',\n                      }}\n                    />\n                    <Text\n                      style={{\n                        fontSize: 12,\n                        fontWeight: 500,\n                        color: '#262626',\n                      }}\n                    >\n                      中: {todoStats.mediumPriorityCount}\n                    </Text>\n                  </Flex>\n                </Tooltip>\n\n                <Tooltip\n                  title={`低优先级任务: ${todoStats.lowPriorityCount}个`}\n                >\n                  <Flex align=\"center\" gap={4}>\n                    <div\n                      style={{\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        background: '#52c41a',\n                      }}\n                    />\n                    <Text\n                      style={{\n                        fontSize: 12,\n                        fontWeight: 500,\n                        color: '#262626',\n                      }}\n                    >\n                      低: {todoStats.lowPriorityCount}\n                    </Text>\n                  </Flex>\n                </Tooltip>\n              </Space>\n            </Flex>\n          </Col>\n\n          {/* 第三列：完成率 */}\n          <Col xs={24} sm={24} md={8} lg={8} xl={8}>\n            <Flex align=\"center\" justify=\"center\">\n              <Tooltip\n                title={`完成率: ${todoStats.completionPercentage}% (${todoStats.completedCount}/${todoStats.totalCount})`}\n              >\n                <Flex align=\"center\" gap={6}>\n                  <Text\n                    style={{ fontSize: 12, fontWeight: 500, color: '#595959' }}\n                  >\n                    完成率:\n                  </Text>\n                  <Progress\n                    percent={todoStats.completionPercentage}\n                    size=\"small\"\n                    style={{ width: 80 }}\n                    strokeColor=\"#52c41a\"\n                    showInfo={false}\n                  />\n                  <Text\n                    style={{ fontSize: 12, fontWeight: 600, color: '#262626' }}\n                  >\n                    {todoStats.completionPercentage}%\n                  </Text>\n                </Flex>\n              </Tooltip>\n            </Flex>\n          </Col>\n        </Row>\n      </div>\n\n      {/* 第二行：标签页 */}\n      <Tabs\n        activeKey={activeTab}\n        onChange={(key) => setActiveTab(key as 'all' | 'pending' | 'completed')}\n        size=\"middle\"\n        style={{ marginBottom: 8 }}\n      >\n        <TabPane tab=\"全部\" key=\"all\" />\n        <TabPane tab=\"待处理\" key=\"pending\" />\n        <TabPane tab=\"已完成\" key=\"completed\" />\n      </Tabs>\n\n      {/* 待办事项列表 */}\n      {error ? (\n        <Alert\n          message=\"TODO数据加载失败\"\n          description={error}\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n      ) : (\n        <Spin spinning={loading}>\n          <List\n            dataSource={filteredPersonalTasks}\n            renderItem={(item) => {\n              return (\n                <List.Item\n                  className=\"todo-item\"\n                  style={{\n                    padding: '10px 16px',\n                    marginBottom: 12,\n                    borderRadius: 8,\n                    background: '#fff',\n                    opacity: item.status === 1 ? 0.7 : 1,\n                    borderLeft: `3px solid ${\n                      item.status === 1\n                        ? '#52c41a'\n                        : item.priority === 3\n                          ? '#ff4d4f'\n                          : item.priority === 2\n                            ? '#faad14'\n                            : '#8c8c8c'\n                    }`,\n                    boxShadow: '0 1px 4px rgba(0,0,0,0.05)',\n                  }}\n                >\n                  <Flex align=\"center\" gap={12} style={{ width: '100%' }}>\n                    {/* 左侧状态和优先级指示器 */}\n                    <Flex vertical align=\"center\">\n                      {item.status === 1 ? (\n                        <Flex\n                          align=\"center\"\n                          justify=\"center\"\n                          style={{\n                            width: 22,\n                            height: 22,\n                            borderRadius: '50%',\n                            background: '#52c41a',\n                          }}\n                        >\n                          <CheckOutlined\n                            style={{ color: '#fff', fontSize: 12 }}\n                          />\n                        </Flex>\n                      ) : (\n                        <div\n                          style={{\n                            width: 18,\n                            height: 18,\n                            borderRadius: '50%',\n                            border: `2px solid ${\n                              item.priority === 3\n                                ? '#ff4d4f'\n                                : item.priority === 2\n                                  ? '#faad14'\n                                  : '#8c8c8c'\n                            }`,\n                          }}\n                        />\n                      )}\n\n                      <div\n                        style={{\n                          width: 2,\n                          height: 24,\n                          background: '#f0f0f0',\n                          marginTop: 4,\n                        }}\n                      />\n                    </Flex>\n\n                    {/* 任务信息区 */}\n                    <Flex vertical style={{ flex: 1 }}>\n                      <Text\n                        style={{\n                          fontSize: 14,\n                          fontWeight: item.priority === 3 ? 500 : 'normal',\n                          textDecoration:\n                            item.status === 1 ? 'line-through' : 'none',\n                          color: item.status === 1 ? '#8c8c8c' : '#262626',\n                        }}\n                      >\n                        {item.title}\n                      </Text>\n\n                      {/* 显示创建日期 */}\n                      <Space align=\"center\" size={6} style={{ marginTop: 4 }}>\n                        <CalendarOutlined\n                          style={{\n                            fontSize: 12,\n                            color: '#8c8c8c',\n                          }}\n                        />\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          创建于:{' '}\n                          {new Date(item.createdAt).toLocaleDateString('zh-CN')}\n                        </Text>\n                      </Space>\n                    </Flex>\n\n                    {/* 操作按钮区 */}\n                    <Dropdown\n                      trigger={['click']}\n                      menu={{\n                        items: [\n                          {\n                            key: 'complete',\n                            label:\n                              item.status === 1 ? '标记未完成' : '标记完成',\n                            icon: (\n                              <CheckOutlined\n                                style={{\n                                  color:\n                                    item.status === 1 ? '#8c8c8c' : '#52c41a',\n                                  fontSize: 14,\n                                }}\n                              />\n                            ),\n                          },\n                          {\n                            key: 'edit',\n                            label: '编辑任务',\n                            icon: <EditOutlined style={{ color: '#8c8c8c' }} />,\n                          },\n                          {\n                            key: 'delete',\n                            label: '删除任务',\n                            icon: (\n                              <DeleteOutlined style={{ color: '#ff4d4f' }} />\n                            ),\n                            danger: true,\n                          },\n                        ],\n                        onClick: ({ key }) => {\n                          if (key === 'complete') {\n                            handleToggleTodoStatus(item.id);\n                          } else if (key === 'edit') {\n                            setEditingTodoId(item.id);\n                            todoForm.setFieldsValue({\n                              name: item.title,\n                              priority: item.priority,\n                            });\n                            setTodoModalVisible(true);\n                          } else if (key === 'delete') {\n                            handleDeleteTodo(item.id);\n                          }\n                        },\n                      }}\n                    >\n                      <Button\n                        type=\"text\"\n                        size=\"small\"\n                        icon={<MoreOutlined />}\n                        style={{ width: 32, height: 32 }}\n                      />\n                    </Dropdown>\n                  </Flex>\n                </List.Item>\n              );\n            }}\n          />\n\n          {/* 待办事项表单模态框 */}\n          <Modal\n            title={editingTodoId ? '编辑待办事项' : '新增待办事项'}\n            open={todoModalVisible}\n            onCancel={() => {\n              setTodoModalVisible(false);\n              todoForm.resetFields();\n            }}\n            onOk={() => {\n              todoForm.submit();\n            }}\n            centered\n            destroyOnClose\n            footer={[\n              <Button key=\"cancel\" onClick={() => setTodoModalVisible(false)}>\n                取消\n              </Button>,\n              <Button\n                key=\"submit\"\n                type=\"primary\"\n                onClick={() => {\n                  todoForm.submit();\n                }}\n                style={{\n                  background: '#1890ff',\n                  borderColor: '#1890ff',\n                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                }}\n              >\n                {editingTodoId ? '更新任务' : '创建任务'}\n              </Button>,\n            ]}\n          >\n            <Form\n              form={todoForm}\n              layout=\"vertical\"\n              onFinish={handleAddOrUpdateTodo}\n              autoComplete=\"off\"\n            >\n              <Form.Item\n                name=\"name\"\n                label=\"任务名称\"\n                rules={[{ required: true, message: '请输入任务名称' }]}\n              >\n                <Input\n                  placeholder=\"请输入任务名称\"\n                  size=\"large\"\n                  style={{ borderRadius: 6 }}\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"priority\"\n                label=\"优先级\"\n                initialValue={2}\n                rules={[{ required: true, message: '请选择优先级' }]}\n              >\n                <Select\n                  size=\"large\"\n                  options={[\n                    { value: 3, label: '高优先级' },\n                    { value: 2, label: '中优先级' },\n                    { value: 1, label: '低优先级' },\n                  ]}\n                  style={{ borderRadius: 6 }}\n                />\n              </Form.Item>\n            </Form>\n          </Modal>\n        </Spin>\n      )}\n    </Card>\n  );\n};\n\nexport default TodoManagement;\n", "import {\n  BarChartOutlined,\n  MailOutlined,\n  PhoneOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Avatar,\n  Card,\n  Col,\n  Flex,\n  Row,\n  Space,\n  Spin,\n  Typography,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type {\n  UserPersonalStatsResponse,\n  UserProfileDetailResponse,\n} from '@/types/api';\n\nconst { Title, Text } = Typography;\n\n/**\n * 用户个人信息卡片组件\n *\n * 这是个人中心页面的用户信息展示组件，负责显示用户的详细信息和个人统计数据。\n * 提供了美观的卡片式布局和响应式设计。\n *\n * 主要功能：\n * 1. 显示用户基本信息（姓名、邮箱、电话等）\n * 2. 显示用户头像和个人资料\n * 3. 显示个人统计数据（车辆、人员、告警等）\n * 4. 提供用户最近活动信息\n *\n * 数据来源：\n * - 用户详细信息：通过UserService.getUserProfile()获取\n * - 个人统计数据：通过UserService.getPersonalStats()获取\n *\n * UI设计：\n * - 渐变背景卡片设计\n * - 响应式布局适配不同屏幕\n * - 图标和文字的合理搭配\n * - 加载状态和错误状态的友好提示\n */\nconst UserProfileCard: React.FC = () => {\n  /**\n   * 用户详细信息状态管理\n   *\n   * 管理用户个人资料的显示状态：\n   * - userInfo: 用户详细信息对象，包含所有个人资料字段\n   * - userInfoLoading: 用户信息加载状态\n   * - userInfoError: 用户信息获取错误信息\n   *\n   * 初始值设置为空字符串和0，确保UI渲染的稳定性。\n   */\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\n    name: '',\n    position: '',\n    email: '',\n    telephone: '',\n    registerDate: '',\n    lastLoginTime: '',\n    lastLoginTeam: '',\n    teamCount: 0,\n    avatar: '',\n  });\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\n\n  /**\n   * 个人统计数据状态管理\n   *\n   * 管理用户的统计数据显示：\n   * - personalStats: 包含各种统计指标的对象\n   * - personalStatsLoading: 统计数据加载状态\n   * - personalStatsError: 统计数据获取错误信息\n   *\n   * 统计数据包括：\n   * - vehicles: 车辆数量\n   * - personnel: 人员数量\n   * - warnings: 警告数量\n   * - alerts: 告警数量\n   */\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>(\n    {\n      vehicles: 0,\n      personnel: 0,\n      warnings: 0,\n      alerts: 0,\n    },\n  );\n  const [statsLoading, setStatsLoading] = useState(true);\n  const [statsError, setStatsError] = useState<string | null>(null);\n\n  // 状态管理\n\n  // 获取用户数据\n  useEffect(() => {\n    console.log('UserProfileCard: useEffect 开始执行');\n\n    const fetchUserData = async () => {\n      try {\n        console.log('UserProfileCard: 开始获取用户数据');\n\n        // 分别获取用户详细信息和统计数据，避免一个失败影响另一个\n        const userDetailPromise = UserService.getUserProfileDetail().catch(\n          (error) => {\n            console.error('获取用户详细信息失败:', error);\n            setUserInfoError('获取用户详细信息失败，请稍后重试');\n            return null;\n          },\n        );\n\n        const statsPromise = UserService.getUserPersonalStats().catch(\n          (error) => {\n            console.error('获取统计数据失败:', error);\n            setStatsError('获取统计数据失败，请稍后重试');\n            return null;\n          },\n        );\n\n        const [userDetail, stats] = await Promise.all([\n          userDetailPromise,\n          statsPromise,\n        ]);\n\n        if (userDetail) {\n          console.log('UserProfileCard: 获取到用户详细信息:', userDetail);\n          setUserInfo(userDetail);\n          setUserInfoError(null);\n        }\n\n        if (stats) {\n          console.log('UserProfileCard: 获取到统计数据:', stats);\n          setPersonalStats(stats);\n          setStatsError(null);\n        }\n      } catch (error) {\n        console.error('获取用户数据时发生未知错误:', error);\n        setUserInfoError('获取用户数据失败，请刷新页面重试');\n        setStatsError('获取统计数据失败，请刷新页面重试');\n      } finally {\n        setUserInfoLoading(false);\n        setStatsLoading(false);\n      }\n    };\n\n    fetchUserData();\n  }, []);\n\n  return (\n    <>\n      {/* 用户信息主卡片 */}\n      {userInfoError ? (\n        <Alert\n          message=\"用户信息加载失败\"\n          description={userInfoError}\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: 24 }}\n        />\n      ) : (\n        <Spin spinning={userInfoLoading}>\n          {/* 使用 Card 组件替代自定义 div */}\n          <Card\n            style={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: 16,\n              color: 'white',\n              position: 'relative',\n              overflow: 'hidden',\n              minHeight: 140, // 改为最小高度，允许内容撑开\n              border: 'none',\n            }}\n            styles={{\n              body: {\n                padding: '16px 16px 16px 16px', // 统一使用16px内边距\n                height: '100%',\n              },\n            }}\n          >\n            {/* 装饰性背景元素 */}\n            <div\n              style={{\n                position: 'absolute',\n                top: -25,\n                right: -25,\n                width: 100,\n                height: 100,\n                background: 'rgba(255,255,255,0.1)',\n                borderRadius: '50%',\n              }}\n            />\n            <div\n              style={{\n                position: 'absolute',\n                bottom: -30,\n                left: -30,\n                width: 80,\n                height: 80,\n                background: 'rgba(255,255,255,0.05)',\n                borderRadius: '50%',\n              }}\n            />\n            <div\n              style={{\n                position: 'absolute',\n                top: '50%',\n                right: '20%',\n                width: 60,\n                height: 60,\n                background: 'rgba(255,255,255,0.03)',\n                borderRadius: '50%',\n                transform: 'translateY(-50%)',\n              }}\n            />\n\n            {/* 主要内容区域 - 使用响应式网格布局 */}\n            <Row\n              gutter={[16, 12]}\n              align=\"middle\"\n              style={{\n                position: 'relative',\n                zIndex: 1,\n                width: '100%',\n                minHeight: '100%',\n              }}\n            >\n              {/* 第一列：用户基本信息区域 */}\n              <Col xs={24} sm={24} md={8} lg={7} xl={6}>\n                <Flex align=\"center\" style={{ minHeight: '80px' }}>\n                  {/* 用户头像 - 使用 Ant Design Avatar 组件 */}\n                  <Avatar\n                    size={64}\n                    shape=\"square\"\n                    style={{\n                      backgroundColor: 'rgba(255,255,255,0.2)',\n                      marginRight: 20,\n                      fontSize: 24,\n                      fontWeight: 600,\n                      border: '2px solid rgba(255,255,255,0.3)',\n                    }}\n                  >\n                    {userInfo.name ? (\n                      userInfo.name.charAt(0).toUpperCase()\n                    ) : (\n                      <UserOutlined />\n                    )}\n                  </Avatar>\n\n                  {/* 用户信息 - 使用 Space 组件垂直布局 */}\n                  <Space direction=\"vertical\" size={4}>\n                    <Title\n                      level={3}\n                      style={{\n                        margin: 0,\n                        color: 'white',\n                        fontSize: 22,\n                        fontWeight: 600,\n                      }}\n                    >\n                      {userInfo.name || '加载中...'}\n                    </Title>\n\n                    {/* 联系信息 - 使用 Space 组件垂直排列 */}\n                    <Space direction=\"vertical\" size={4}>\n                      {userInfo.email && (\n                        <Space size={6} align=\"center\">\n                          <MailOutlined\n                            style={{\n                              fontSize: 13,\n                              color: 'rgba(255,255,255,0.9)',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              color: 'rgba(255,255,255,0.9)',\n                              fontSize: 12,\n                            }}\n                          >\n                            {userInfo.email}\n                          </Text>\n                        </Space>\n                      )}\n                      {userInfo.telephone && (\n                        <Space size={6} align=\"center\">\n                          <PhoneOutlined\n                            style={{\n                              fontSize: 13,\n                              color: 'rgba(255,255,255,0.9)',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              color: 'rgba(255,255,255,0.9)',\n                              fontSize: 12,\n                            }}\n                          >\n                            {userInfo.telephone}\n                          </Text>\n                        </Space>\n                      )}\n                    </Space>\n\n                    {/* 注册日期 */}\n                    {userInfo.registerDate && (\n                      <Text\n                        style={{\n                          fontSize: 13,\n                          color: 'rgba(255,255,255,0.8)',\n                          fontWeight: 500,\n                        }}\n                      >\n                        注册于 {userInfo.registerDate}\n                      </Text>\n                    )}\n                  </Space>\n                </Flex>\n              </Col>\n\n              {/* 第二列：数据概览区域 - 两行结构 */}\n              <Col xs={24} sm={24} md={8} lg={10} xl={12}>\n                <Flex\n                  vertical\n                  justify=\"center\"\n                  style={{\n                    minHeight: '80px',\n                    textAlign: 'center',\n                    padding: '8px 0',\n                  }}\n                >\n                  {/* 第一行：数据概览标题和图标 */}\n                  <Space\n                    align=\"center\"\n                    style={{\n                      justifyContent: 'center',\n                      marginBottom: 16,\n                    }}\n                  >\n                    <BarChartOutlined\n                      style={{\n                        fontSize: 16,\n                        color: 'rgba(255,255,255,0.9)',\n                      }}\n                    />\n                    <Text\n                      style={{\n                        color: 'rgba(255,255,255,0.9)',\n                        fontSize: 14,\n                        fontWeight: 600,\n                      }}\n                    >\n                      数据概览\n                    </Text>\n                  </Space>\n\n                  {/* 第二行：指标卡片 */}\n                  {statsError ? (\n                    <Text\n                      style={{ fontSize: 12, color: 'rgba(255,255,255,0.8)' }}\n                    >\n                      数据加载失败\n                    </Text>\n                  ) : (\n                    <Spin spinning={statsLoading}>\n                      <Row gutter={[4, 8]} justify=\"center\">\n                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                          <div style={{ textAlign: 'center' }}>\n                            <div\n                              style={{\n                                fontSize: 16,\n                                fontWeight: 700,\n                                color: 'white',\n                                lineHeight: 1,\n                              }}\n                            >\n                              {personalStats.vehicles}\n                            </div>\n                            <div\n                              style={{\n                                fontSize: 11,\n                                color: 'rgba(255,255,255,0.8)',\n                                marginTop: 2,\n                              }}\n                            >\n                              车辆\n                            </div>\n                          </div>\n                        </Col>\n                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                          <div style={{ textAlign: 'center' }}>\n                            <div\n                              style={{\n                                fontSize: 16,\n                                fontWeight: 700,\n                                color: 'white',\n                                lineHeight: 1,\n                              }}\n                            >\n                              {personalStats.personnel}\n                            </div>\n                            <div\n                              style={{\n                                fontSize: 11,\n                                color: 'rgba(255,255,255,0.8)',\n                                marginTop: 2,\n                              }}\n                            >\n                              人员\n                            </div>\n                          </div>\n                        </Col>\n                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                          <div style={{ textAlign: 'center' }}>\n                            <div\n                              style={{\n                                fontSize: 16,\n                                fontWeight: 700,\n                                color: 'white',\n                                lineHeight: 1,\n                              }}\n                            >\n                              {personalStats.warnings}\n                            </div>\n                            <div\n                              style={{\n                                fontSize: 11,\n                                color: 'rgba(255,255,255,0.8)',\n                                marginTop: 2,\n                              }}\n                            >\n                              预警\n                            </div>\n                          </div>\n                        </Col>\n                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                          <div style={{ textAlign: 'center' }}>\n                            <div\n                              style={{\n                                fontSize: 16,\n                                fontWeight: 700,\n                                color: 'white',\n                                lineHeight: 1,\n                              }}\n                            >\n                              {personalStats.alerts}\n                            </div>\n                            <div\n                              style={{\n                                fontSize: 11,\n                                color: 'rgba(255,255,255,0.8)',\n                                marginTop: 2,\n                              }}\n                            >\n                              告警\n                            </div>\n                          </div>\n                        </Col>\n                      </Row>\n                    </Spin>\n                  )}\n                </Flex>\n              </Col>\n\n              {/* 第三列：最近活动信息 */}\n              <Col xs={24} sm={24} md={8} lg={7} xl={6}>\n                <Flex\n                  vertical\n                  justify=\"center\"\n                  style={{ minHeight: '80px', padding: '8px 0' }}\n                >\n                  <Space direction=\"vertical\" size={10}>\n                    <Space direction=\"vertical\" size={4}>\n                      <Text\n                        style={{\n                          fontSize: 12,\n                          color: 'rgba(255,255,255,0.8)',\n                          fontWeight: 500,\n                        }}\n                      >\n                        最后登录时间\n                      </Text>\n                      <Text\n                        style={{\n                          fontSize: 14,\n                          color: 'white',\n                          fontWeight: 600,\n                          lineHeight: 1.3,\n                        }}\n                      >\n                        {userInfo.lastLoginTime || '暂无记录'}\n                      </Text>\n                    </Space>\n                    <Space direction=\"vertical\" size={4}>\n                      <Text\n                        style={{\n                          fontSize: 12,\n                          color: 'rgba(255,255,255,0.8)',\n                          fontWeight: 500,\n                        }}\n                      >\n                        最后登录团队\n                      </Text>\n                      <Text\n                        style={{\n                          fontSize: 14,\n                          color: 'white',\n                          fontWeight: 600,\n                          lineHeight: 1.3,\n                        }}\n                      >\n                        {userInfo.lastLoginTeam || '暂无记录'}\n                      </Text>\n                    </Space>\n                  </Space>\n                </Flex>\n              </Col>\n            </Row>\n          </Card>\n        </Spin>\n      )}\n    </>\n  );\n};\n\nexport default UserProfileCard;\n", "import { useModel } from '@umijs/max';\nimport { Card, Col, Row, Spin } from 'antd';\nimport React from 'react';\nimport UserFloatButton from '@/components/FloatButton';\nimport TeamListCard from './TeamListCard';\nimport TodoManagement from './TodoManagement';\nimport UserProfileCard from './UserProfileCard';\n\n/**\n * 个人中心页面组件\n *\n * 这是用户的个人中心主页面，提供用户信息管理、团队管理、待办事项等功能。\n * 是用户进行个人设置和团队操作的主要入口页面。\n *\n * 页面功能：\n * 1. 用户个人信息展示和编辑\n * 2. 团队列表显示和团队切换\n * 3. 个人待办事项管理\n * 4. 全局浮动操作按钮\n *\n * 页面结构：\n * - 顶部：用户个人信息卡片（全宽显示）\n * - 左侧：待办事项管理（响应式布局）\n * - 右侧：团队列表和操作（响应式布局）\n * - 浮动：全局操作按钮\n *\n * 权限控制：\n * - 需要用户登录才能访问\n * - 自动检查登录状态并重定向\n * - 支持登录状态变化的实时响应\n *\n * 响应式设计：\n * - 移动端：垂直堆叠布局\n * - 桌面端：左右分栏布局\n * - 自适应不同屏幕尺寸\n */\nconst PersonalCenterPage: React.FC = () => {\n  /**\n   * 全局状态管理\n   *\n   * 从UmiJS全局状态中获取用户信息和加载状态：\n   * - initialState: 包含用户和团队信息的全局状态\n   * - loading: 全局状态的加载状态\n   */\n  const { initialState, loading } = useModel('@@initialState');\n\n\n\n  /**\n   * 加载状态处理\n   *\n   * 当全局状态正在初始化时，显示加载界面。\n   * 这确保了用户在状态加载完成前看到友好的加载提示。\n   */\n  if (loading) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}\n      >\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在加载用户信息...</div>\n      </div>\n    );\n  }\n\n  /**\n   * 登录状态检查已由应用级路由守卫处理\n   *\n   * 移除页面级别的登录检查，避免与应用级路由守卫冲突。\n   * 应用级路由守卫在 app.tsx 中的 onPageChange 函数已经处理了\n   * 用户登录状态检查和重定向逻辑，无需在页面组件中重复检查。\n   *\n   * 这样可以避免登录成功后的状态更新时序问题，确保用户\n   * 一次登录成功后能够正常访问个人中心页面。\n   */\n\n  return (\n    <>\n      {/* 页面主容器 */}\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff', // 浅蓝色背景，营造清新的视觉效果\n          padding: '12px 12px 24px 12px', // 移动端优化：减少左右边距，增加底部边距\n        }}\n      >\n        {/*\n         * 主内容卡片容器\n         *\n         * 使用Card组件作为主要内容的容器，提供：\n         * 1. 统一的视觉边界和阴影效果\n         * 2. 响应式的内边距设置\n         * 3. 圆角设计提升视觉体验\n         * 4. 全高度布局适配不同屏幕\n         */}\n        <Card\n          style={{\n            width: '100%',\n            minHeight: 'calc(100vh - 48px)', // 减去外层padding的高度\n            borderRadius: '12px', // 圆角设计\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)', // 轻微阴影效果\n          }}\n          styles={{\n            body: {\n              padding: '24px', // 内容区域的内边距\n            },\n          }}\n        >\n          {/*\n           * 响应式网格布局\n           *\n           * 使用Ant Design的Row/Col组件实现响应式布局：\n           * - 移动端：垂直堆叠，所有组件占满宽度\n           * - 桌面端：个人信息全宽，待办事项和团队列表左右分栏\n           * - gutter: 组件间距设置\n           * - margin: 0: 避免Row组件的默认负边距影响布局\n           */}\n          <Row gutter={[16, 16]} style={{ margin: 0 }}>\n            {/*\n             * 个人信息卡片区域\n             *\n             * 显示用户的基本信息、头像、联系方式等。\n             * 全宽显示，作为页面的头部信息区域。\n             */}\n            <Col xs={24} style={{ marginBottom: 8 }}>\n              <UserProfileCard />\n            </Col>\n\n            {/*\n             * 待办事项管理区域\n             *\n             * 个人待办事项的管理界面，支持添加、编辑、删除待办事项。\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据左半部分\n             */}\n            <Col\n              xs={24}\n              sm={24}\n              md={24}\n              lg={12}\n              xl={12}\n              xxl={12}\n              style={{ marginBottom: 8 }}\n            >\n              <TodoManagement />\n            </Col>\n\n            {/*\n             * 团队列表管理区域\n             *\n             * 显示用户所属的团队列表，支持团队切换和创建新团队。\n             * 这是团队管理功能的主要入口。\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据右半部分\n             */}\n            <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>\n              <TeamListCard />\n            </Col>\n          </Row>\n        </Card>\n      </div>\n\n      {/*\n       * 全局浮动操作按钮\n       *\n       * 提供快速访问常用功能的浮动按钮，如：\n       * - 快速创建团队\n       * - 用户设置\n       * - 帮助信息\n       *\n       * 位置固定在页面右下角，不受页面滚动影响。\n       */}\n      <UserFloatButton />\n\n\n\n\n    </>\n  );\n};\n\nexport default PersonalCenterPage;\n", "/**\n * TODO服务\n */\n\nimport type {\n  CreateTodoRequest,\n  TodoResponse,\n  TodoStatsResponse,\n  UpdateTodoRequest,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\n\nexport class TodoService {\n  /**\n   * 获取用户的TODO列表\n   */\n  static async getUserTodos(): Promise<TodoResponse[]> {\n    const response = await apiRequest.get<TodoResponse[]>('/todos');\n    return response.data;\n  }\n\n  /**\n   * 创建TODO\n   */\n  static async createTodo(request: CreateTodoRequest): Promise<TodoResponse> {\n    const response = await apiRequest.post<TodoResponse>('/todos', request);\n    return response.data;\n  }\n\n  /**\n   * 更新TODO\n   */\n  static async updateTodo(\n    id: number,\n    request: UpdateTodoRequest,\n  ): Promise<TodoResponse> {\n    const response = await apiRequest.put<TodoResponse>(\n      `/todos/${id}`,\n      request,\n    );\n    return response.data;\n  }\n\n  /**\n   * 删除TODO\n   */\n  static async deleteTodo(id: number): Promise<void> {\n    await apiRequest.delete(`/todos/${id}`);\n  }\n\n  /**\n   * 获取TODO统计信息\n   */\n  static async getTodoStats(): Promise<TodoStatsResponse> {\n    const response = await apiRequest.get<TodoStatsResponse>('/todos/stats');\n    return response.data;\n  }\n}\n", "/**\n * 团队选择状态管理工具函数\n * 用于跟踪用户是否已经主动选择过团队，以区分初始登录状态和主动选择状态\n */\n\n// 团队选择历史的本地存储键\nconst TEAM_SELECTION_KEY = 'user_team_selection_history';\n\n/**\n * 获取用户的团队选择历史\n * @param userId 用户ID\n * @returns 用户选择过的团队ID集合\n */\nexport const getUserTeamSelectionHistory = (userId: number): Set<number> => {\n  try {\n    const history = localStorage.getItem(`${TEAM_SELECTION_KEY}_${userId}`);\n    if (history) {\n      return new Set(JSON.parse(history));\n    }\n  } catch (error) {\n    console.error('获取团队选择历史失败:', error);\n  }\n  return new Set();\n};\n\n/**\n * 记录用户选择了某个团队\n * @param userId 用户ID\n * @param teamId 团队ID\n */\nexport const recordTeamSelection = (userId: number, teamId: number): void => {\n  try {\n    const history = getUserTeamSelectionHistory(userId);\n    history.add(teamId);\n    localStorage.setItem(`${TEAM_SELECTION_KEY}_${userId}`, JSON.stringify([...history]));\n    console.log(`记录团队选择: 用户${userId}选择了团队${teamId}`);\n  } catch (error) {\n    console.error('记录团队选择历史失败:', error);\n  }\n};\n\n/**\n * 检查用户是否曾经选择过某个团队\n * @param userId 用户ID\n * @param teamId 团队ID\n * @returns 是否曾经选择过该团队\n */\nexport const hasUserSelectedTeam = (userId: number, teamId: number): boolean => {\n  const history = getUserTeamSelectionHistory(userId);\n  return history.has(teamId);\n};\n\n/**\n * 清除用户的团队选择历史（用于注销等场景）\n * @param userId 用户ID\n */\nexport const clearUserTeamSelectionHistory = (userId: number): void => {\n  try {\n    localStorage.removeItem(`${TEAM_SELECTION_KEY}_${userId}`);\n    console.log(`清除用户${userId}的团队选择历史`);\n  } catch (error) {\n    console.error('清除团队选择历史失败:', error);\n  }\n};\n\n/**\n * 获取所有用户的团队选择历史键（用于调试）\n * @returns 所有相关的localStorage键\n */\nexport const getAllTeamSelectionKeys = (): string[] => {\n  const keys: string[] = [];\n  for (let i = 0; i < localStorage.length; i++) {\n    const key = localStorage.key(i);\n    if (key && key.startsWith(TEAM_SELECTION_KEY)) {\n      keys.push(key);\n    }\n  }\n  return keys;\n};\n"], "names": [], "mappings": ";;;;;;;4BAy3BA;;;eAAA;;;;;;8BA/2BO;4BAC2B;6BAa3B;wEACoC;iCACf;6BACA;mCAMrB;2CACkD;;;;;;;;;;AAEzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;AAKlC,UAAU;AACV,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEhB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BC,GACD,MAAM,eAAyB;;IAC7B;;;;;;;;GAQC,GACD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAgB;IAEtE;;;;;;GAMC,GAED;;;;;;;GAOC,GACD,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IACnD,MAAM,cAAc,yBAAA,mCAAA,aAAc,WAAW;IAE7C;;;;;;;;;;;;GAYC,GACD,MAAM,qBAAqB,IAAA,qCAAyB;IACpD,MAAM,gBAAgB,IAAA,qCAAyB;IAC/C,MAAM,iBAAiB,IAAA,iCAAqB;IAE5C,gBAAgB;IAChB,8BAA8B;IAC9B,qCAAqC;IACrC,uBAAuB;IACvB,gCAAgC;IAChC,MAAM,qBAAqB,CAAC,CAC1B,CAAA,kBACA,sBACA,eACA,YAAY,EAAE,KAAK,sBACnB,iBACA,IAAA,uCAAmB,EAAC,eAAe,mBAAkB;IAGvD,wCAAwC;IACxC,MAAM,sBAAsB,qBAAqB,qBAAqB;IAEtE,OAAO;IACP,QAAQ,GAAG,CAAC,sBAAsB;QAChC,WAAW,EAAE,wBAAA,kCAAA,YAAa,EAAE;QAC5B;QACA;QACA;QACA;QACA;QACA,4BAA4B,iBAAiB,qBAAqB,IAAA,uCAAmB,EAAC,eAAe,sBAAsB;QAC3H,yBAAyB,CAAC,EAAC,yBAAA,mCAAA,aAAc,WAAW;IACtD;IAEA,WAAW;IACX,IAAA,gBAAS,EAAC;QACR,MAAM,aAAa;YACjB,IAAI;gBACF,WAAW;gBACX,SAAS;gBACT,MAAM,YAAY,MAAM,iBAAW,CAAC,qBAAqB;gBACzD,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA,mBAAmB;QACnB,IAAI,yBAAA,mCAAA,aAAc,WAAW,EAC3B;IAEJ,GAAG;QAAC,yBAAA,mCAAA,aAAc,WAAW;KAAC;IAE9B,mBAAmB;IACnB,IAAA,gBAAS,EAAC;QACR,4CAA4C;QAC5C,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,GAAE;YAC9B,SAAS,EAAE;YACX,SAAS;YACT,WAAW;YACX,mBAAmB;QACrB;IACF,GAAG;QAAC,yBAAA,mCAAA,aAAc,WAAW;KAAC;IAE9B,aAAa;IACb,IAAA,gBAAS,EAAC;QACR,QAAQ,GAAG,CAAC,aAAa;YACvB,WAAW,EAAE,wBAAA,kCAAA,YAAa,EAAE;YAC5B;YACA;QACF;IACF,GAAG;QAAC,wBAAA,kCAAA,YAAa,EAAE;QAAE;QAAqB;KAAmB;IAE7D,2BAA2B;IAE3B;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BC,GACD,MAAM,mBAAmB,OAAO,QAAgB;QAC9C;;;;;KAKC,GACD,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,GAC5B;QAGF,IAAI;YACF;;;;;;;OAOC,GACD,mBAAmB;YAEnB;;;;;OAKC,GACD,IAAI,WAAW,qBAAqB;gBAClC,YAAO,CAAC,IAAI,CAAC;gBACb;YACF;YAEA;;;;;;;OAOC,GACD,MAAM,WAAW,MAAM,qBAAW,CAAC,UAAU,CAAC;gBAAE;YAAO;YAEvD;;;;;;;OAOC,GACD,IACE,SAAS,oBAAoB,IAC7B,SAAS,IAAI,IACb,SAAS,IAAI,CAAC,EAAE,KAAK,QACrB;gBACA;;;;;;;SAOC,GACD,IAAI,eACF,IAAA,uCAAmB,EAAC,eAAe;gBAGrC;;;;;;;;;;;SAWC,GACD,IACE,CAAA,yBAAA,mCAAA,aAAc,aAAa,MAC3B,yBAAA,mCAAA,aAAc,aAAa,KAC3B,iBAEA,eAAe;gBACf,QAAQ,GAAG,CAAC;oBACV,aAAa,aAAa;oBAC1B,aAAa,aAAa;iBAC3B,EACE,IAAI,CAAC,CAAC,CAAC,aAAa,YAAY;oBAC/B,oBAAoB;oBACpB,IAAI,eAAe,YAAY,EAAE,KAAK,QACpC,gBAAgB;wBACd,GAAG,YAAY;wBACf;wBACA;oBACF;gBAEJ,GACC,KAAK,CAAC,CAAC;oBACN,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,qBAAqB;gBACvB;gBAGJ;;;;;SAKC,GACD,YAAO,CAAC,IAAI,CAAC;YACf;QASF,EAAE,OAAO,OAAY;QACnB;;;;;;;;;;;;OAYC,GACD,iBAAiB;QACnB,SAAU;YACR;;;;;OAKC,GACD,mBAAmB;QACrB;IACF;IAEA,qBACE;;0BAEE,2BAAC;gBAAM,yBAAyB;oBAAE,QAAQ;gBAAO;;;;;;0BAEjD,2BAAC,UAAI;gBACH,WAAU;gBACV,OAAO;oBACL,cAAc;oBACd,WAAW;oBACX,QAAQ;oBACR,YAAY;gBACd;gBACA,qBACE,2BAAC;oBACC,OAAO;oBACP,OAAO;wBACL,QAAQ;wBACR,YAAY;wBACZ,sBAAsB;wBACtB,qBAAqB;wBACrB,YAAY;oBACd;8BACD;;;;;;0BAKF,sBACC,2BAAC,WAAK;oBACJ,SAAQ;oBACR,aAAa;oBACb,MAAK;oBACL,QAAQ;oBACR,OAAO;wBAAE,cAAc;oBAAG;;;;;yCAG5B,2BAAC,UAAI;oBAAC,UAAU;8BACb,EAAC,yBAAA,mCAAA,aAAc,WAAW,kBACzB,2BAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAY;kCACtD,cAAA,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;+BAEvB,MAAM,MAAM,KAAK,KAAK,CAAC,wBACzB,2BAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAY;kCACtD,cAAA,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;6CAGzB,2BAAC,UAAI;wBACH,YAAY;wBACZ,YAAY,CAAC;gCAmRQ,aAiCA,cAiCA,cAiCA;iDArXnB,2BAAC,UAAI,CAAC,IAAI;0CACR,cAAA,2BAAC,UAAI;oCACH,WAAU;oCACV,OAAO;wCACL,YACE,wBAAwB,KAAK,EAAE,GAC3B,8CACA;wCACN,cAAc;wCACd,WACE,wBAAwB,KAAK,EAAE,GAC3B,uCACA;wCACN,OAAO;wCACP,YAAY,CAAC,UAAU,EAAE,KAAK,SAAS,GAAG,YAAY,UAAU,CAAC;wCACjE,YAAY;wCACZ,QACE,wBAAwB,KAAK,EAAE,GAC3B,sBACA;wCACN,SAAS;wCACT,UAAU;wCACV,UAAU;oCACZ;oCACA,SAAS;oCACT,cAAc,CAAC;wCACb,IAAI,wBAAwB,KAAK,EAAE,EAAE;4CACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAC7B;wCACJ;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,wBAAwB,KAAK,EAAE,EAAE;4CACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAC7B;wCACJ;oCACF;8CAGA,cAAA,2BAAC,SAAG;wCACF,QAAQ;4CAAC;4CAAG;yCAAE;wCACd,OAAM;wCACN,OAAO;4CAAE,OAAO;wCAAO;;0DAGvB,2BAAC,SAAG;gDAAC,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;0DACvC,cAAA,2BAAC,UAAI;oDAAC,QAAQ;oDAAC,KAAK;oDAAG,WAAU;;sEAE/B,2BAAC,UAAI;4DAAC,OAAM;4DAAS,KAAK;4DAAG,MAAK;;8EAChC,2BAAC;oEACC,OAAO;wEACL,QAAQ;wEACR,SAAS;wEACT,cAAc;wEACd,YAAY;wEACZ,SAAS;wEACT,YAAY;wEACZ,KAAK;oEACP;oEACA,SAAS,IACP,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;oEAErC,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAC9B;oEACJ;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAC9B;oEACJ;;sFAEA,2BAAC;4EACC,MAAM;4EACN,OAAO;gFACL,UAAU;gFACV,OACE,wBAAwB,KAAK,EAAE,GAC3B,YACA;gFACN,YAAY;4EACd;sFAEC,KAAK,IAAI;;;;;;sFAEZ,2BAAC,oBAAa;4EACZ,OAAO;gFACL,UAAU;gFACV,OACE,wBAAwB,KAAK,EAAE,GAC3B,YACA;gFACN,eAAe;gFACf,SAAS;gFACT,YAAY;4EACd;;;;;;;;;;;;gEAKH,wBAAwB,KAAK,EAAE,kBAC9B,2BAAC;oEACC,OAAO;wEACL,YAAY;wEACZ,OAAO;wEACP,SAAS;wEACT,cAAc;wEACd,UAAU;wEACV,YAAY;oEACd;8EACD;;;;;;gEAOF,oBAAoB,KAAK,EAAE,kBAC1B,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;;sFACxB,2BAAC,UAAI;4EAAC,MAAK;;;;;;sFACX,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAI,OAAO;4EAAO;sFAAG;;;;;;;;;;;;;;;;;;sEAQpD,2BAAC,UAAI;4DAAC,OAAM;4DAAS,KAAK;4DAAI,MAAK;4DAAO,WAAU;;8EAClD,2BAAC,aAAO;oEACN,OAAO,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC;8EAEpE,cAAA,2BAAC,UAAI;wEAAC,OAAM;wEAAS,KAAK;;0FACxB,2BAAC,0BAAmB;gFAClB,OAAO;oFAAE,OAAO;oFAAW,UAAU;gFAAG;;;;;;0FAE1C,2BAAC;gFACC,OAAO;oFAAE,UAAU;oFAAI,OAAO;gFAAU;;oFACzC;oFACM,IAAI,KACP,KAAK,SAAS,EACd,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;gEAM1B,KAAK,UAAU,kBACd,2BAAC,aAAO;oEACN,OAAO,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,UAAU,EAAE,cAAc,CAAC,SAAS,CAAC;8EAErE,cAAA,2BAAC,UAAI;wEAAC,OAAM;wEAAS,KAAK;;0FACxB,2BAAC,mBAAY;gFACX,OAAO;oFAAE,OAAO;oFAAW,UAAU;gFAAG;;;;;;0FAE1C,2BAAC;gFACC,OAAO;oFAAE,UAAU;oFAAI,OAAO;gFAAU;;oFACzC;oFACM,IAAI,KACP,KAAK,UAAU,EACf,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;8EAM7B,2BAAC,aAAO;oEACN,OAAO,CAAC,MAAM,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC;8EAEnC,cAAA,2BAAC,UAAI;wEAAC,OAAM;wEAAS,KAAK;;0FACxB,2BAAC,mBAAY;gFACX,OAAO;oFAAE,OAAO;oFAAW,UAAU;gFAAG;;;;;;0FAE1C,2BAAC;gFACC,OAAO;oFAAE,UAAU;oFAAI,OAAO;gFAAU;;oFAEvC,KAAK,WAAW;oFAAC;;;;;;;;;;;;;;;;;;;;;;;;sEAO1B,2BAAC,UAAI;4DAAC,OAAM;4DAAS,KAAK;4DAAG,MAAK;4DAAO,WAAU;;8EAEjD,2BAAC;oEACC,OAAO;wEACL,YAAY,KAAK,SAAS,GACtB,YACA;wEACJ,OAAO;wEACP,SAAS;wEACT,cAAc;wEACd,UAAU;wEACV,YAAY;wEACZ,SAAS;wEACT,YAAY;wEACZ,KAAK;oEACP;8EAEC,KAAK,SAAS,iBACb;;0FACE,2BAAC,oBAAa;gFAAC,OAAO;oFAAE,UAAU;gFAAE;;;;;;4EAAK;;qGAI3C;;0FACE,2BAAC,mBAAY;gFAAC,OAAO;oFAAE,UAAU;gFAAE;;;;;;4EAAK;;;;;;;;8EAO9C,2BAAC;oEACC,OAAO;wEACL,YAAY,KAAK,QAAQ,GAAG,YAAY;wEACxC,OAAO;wEACP,SAAS;wEACT,cAAc;wEACd,UAAU;wEACV,YAAY;wEACZ,SAAS;wEACT,YAAY;wEACZ,KAAK;oEACP;8EAEC,KAAK,QAAQ,iBACZ;;0FACE,2BAAC,0BAAmB;gFAAC,OAAO;oFAAE,UAAU;gFAAE;;;;;;4EAAK;;qGAIjD;;0FACE,2BAAC,0BAAmB;gFAAC,OAAO;oFAAE,UAAU;gFAAE;;;;;;4EAAK;;;;;;;;;;;;;;;;;;;;;;;;;0DAU3D,2BAAC,SAAG;gDAAC,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;0DACvC,cAAA,2BAAC,SAAG;oDACF,QAAQ;wDAAC;wDAAG;qDAAE;oDACd,SAAS;wDAAE,IAAI;wDAAS,IAAI;oDAAM;;sEAGlC,2BAAC,SAAG;4DAAC,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;sEACnC,cAAA,2BAAC;gEACC,OAAO;oEACL,YAAY;oEACZ,QAAQ;oEACR,cAAc;oEACd,SAAS;oEACT,WAAW;oEACX,UAAU;gEACZ;0EAEA,cAAA,2BAAC,UAAI;oEAAC,QAAQ;oEAAC,OAAM;oEAAS,KAAK;;sFACjC,2BAAC,kBAAW;4EACV,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;sFAE1C,2BAAC;4EACC,MAAM;4EACN,OAAO;gFACL,UAAU;gFACV,OAAO;gFACP,YAAY;4EACd;sFAEC,EAAA,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,QAAQ,KAAI;;;;;;sFAE3B,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAG,OAAO;4EAAO;sFAAG;;;;;;;;;;;;;;;;;;;;;;sEAQnD,2BAAC,SAAG;4DAAC,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;sEACnC,cAAA,2BAAC;gEACC,OAAO;oEACL,YAAY;oEACZ,QAAQ;oEACR,cAAc;oEACd,SAAS;oEACT,WAAW;oEACX,UAAU;gEACZ;0EAEA,cAAA,2BAAC,UAAI;oEAAC,QAAQ;oEAAC,OAAM;oEAAS,KAAK;;sFACjC,2BAAC,mBAAY;4EACX,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;sFAE1C,2BAAC;4EACC,MAAM;4EACN,OAAO;gFACL,UAAU;gFACV,OAAO;gFACP,YAAY;4EACd;sFAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,SAAS,KAAI;;;;;;sFAE5B,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAG,OAAO;4EAAO;sFAAG;;;;;;;;;;;;;;;;;;;;;;sEAQnD,2BAAC,SAAG;4DAAC,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;sEACnC,cAAA,2BAAC;gEACC,OAAO;oEACL,YAAY;oEACZ,QAAQ;oEACR,cAAc;oEACd,SAAS;oEACT,WAAW;oEACX,UAAU;gEACZ;0EAEA,cAAA,2BAAC,UAAI;oEAAC,QAAQ;oEAAC,OAAM;oEAAS,KAAK;;sFACjC,2BAAC,gCAAyB;4EACxB,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;sFAE1C,2BAAC;4EACC,MAAM;4EACN,OAAO;gFACL,UAAU;gFACV,OAAO;gFACP,YAAY;4EACd;sFAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,QAAQ,KAAI;;;;;;sFAE3B,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAG,OAAO;4EAAO;sFAAG;;;;;;;;;;;;;;;;;;;;;;sEAQnD,2BAAC,SAAG;4DAAC,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;sEACnC,cAAA,2BAAC;gEACC,OAAO;oEACL,YAAY;oEACZ,QAAQ;oEACR,cAAc;oEACd,SAAS;oEACT,WAAW;oEACX,UAAU;gEACZ;0EAEA,cAAA,2BAAC,UAAI;oEAAC,QAAQ;oEAAC,OAAM;oEAAS,KAAK;;sFACjC,2BAAC,gCAAyB;4EACxB,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;sFAE1C,2BAAC;4EACC,MAAM;4EACN,OAAO;gFACL,UAAU;gFACV,OAAO;gFACP,YAAY;4EACd;sFAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,OAAO,KAAI;;;;;;sFAE1B,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAG,OAAO;4EAAO;sFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB/E;GA1uBM;;QA+BsC,aAAQ;;;KA/B9C;IA4uBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BC/Of;;;eAAA;;;;;;8BAloBO;6BAqBA;wEACoC;6BACf;;;;;;;;;;AAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAI;AASxB,MAAM,iBAAgD;;IACpD,aAAa;IACb,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAiB,EAAE;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAoB;QAC5D,mBAAmB;QACnB,qBAAqB;QACrB,kBAAkB;QAClB,YAAY;QACZ,gBAAgB;QAChB,sBAAsB;IACxB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;IAElD,WAAW;IACX,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;IAElE,QAAQ;IACR,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EACxC;IAEF,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAE7C,WAAW;IACX,IAAA,gBAAS,EAAC;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,QAAQ,GAAG,CAAC;gBAEZ,8BAA8B;gBAC9B,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;oBACrD,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO,EAAE;gBACX;gBAEA,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;oBACrD,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;wBACL,mBAAmB;wBACnB,qBAAqB;wBACrB,kBAAkB;wBAClB,YAAY;wBACZ,gBAAgB;wBAChB,sBAAsB;oBACxB;gBACF;gBAEA,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAAC;oBAAc;iBAAa;gBAErE,QAAQ,GAAG,CAAC,8BAA8B;gBAC1C,QAAQ,GAAG,CAAC,4BAA4B;gBAExC,iBAAiB;gBACjB,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,wBAAwB,AAAC,CAAA,iBAAiB,EAAE,AAAD,EAAG,MAAM,CAAC,CAAC;QAC1D,SAAS;QACT,IAAI,cAAc,aAAa,KAAK,MAAM,KAAK,GAAG,OAAO;QACzD,IAAI,cAAc,eAAe,KAAK,MAAM,KAAK,GAAG,OAAO;QAE3D,WAAW;QACX,IACE,cACA,CAAC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEzD,OAAO;QAGT,OAAO;IACT;IAEA,WAAW;IACX,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,OAAO,cAAc,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YAChD,IAAI,CAAC,MACH;YAGF,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI,IAAI;YAE1C,MAAM,iBAAW,CAAC,UAAU,CAAC,IAAI;gBAAE,QAAQ;YAAU;YAErD,SAAS;YACT,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAU,IAAI;YAItD,SAAS;YACT,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;YACnB,kBAAkB;YACpB;QACF,EAAE,OAAO,OAAO;QACd,iBAAiB;QACnB;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,IAAI,eAAe;gBACjB,WAAW;gBACX,MAAM,cAAc,MAAM,iBAAW,CAAC,UAAU,CAAC,eAAe;oBAC9D,OAAO,OAAO,IAAI;oBAClB,UAAU,OAAO,QAAQ;gBAC3B;gBAEA,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,gBAAgB,cAAc;YAGhD,OAAO;gBACL,UAAU;gBACV,MAAM,UAAU,MAAM,iBAAW,CAAC,UAAU,CAAC;oBAC3C,OAAO,OAAO,IAAI;oBAClB,UAAU,OAAO,QAAQ;gBAC3B;gBAEA,iBAAiB;oBAAC;uBAAY;iBAAc;YAC9C;YAEA,SAAS;YACT,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;YACnB,kBAAkB;YACpB;YAEA,aAAa;YACb,oBAAoB;YACpB,iBAAiB;YACjB,SAAS,WAAW;QACtB,EAAE,OAAO,OAAO;QACd,iBAAiB;QACnB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,iBAAW,CAAC,UAAU,CAAC;YAC7B,iBAAiB,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;YAE5D,SAAS;YACT,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;YACnB,kBAAkB;YACpB;QACF,EAAE,OAAO,OAAO;QACd,iBAAiB;QACnB;IACF;IAEA,qBACE,2BAAC,UAAI;QACH,WAAU;QACV,OAAO;YACL,cAAc;YACd,WAAW;YACX,QAAQ;YACR,YAAY;QACd;QACA,qBACE,2BAAC,UAAI;YAAC,SAAQ;YAAgB,OAAM;sBAClC,cAAA,2BAAC;gBAAK,MAAM;0BAAC;;;;;;;;;;;;0BAKjB,2BAAC;gBACC,OAAO;oBACL,cAAc;oBACd,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,QAAQ;gBACV;0BAGA,cAAA,2BAAC,SAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;oBAAE,OAAM;;sCAE3B,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,IAAI;sCACrC,cAAA,2BAAC,UAAI;gCAAC,OAAM;gCAAS,KAAK;gCAAI,OAAO;oCAAE,OAAO;gCAAO;;kDACnD,2BAAC,WAAK,CAAC,MAAM;wCACX,aAAY;wCACZ,UAAU;wCACV,sBAAQ,2BAAC,qBAAc;;;;;wCACvB,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,OAAO;4CAAE,MAAM;wCAAE;wCACjB,MAAK;;;;;;kDAGP,2BAAC,YAAM;wCACL,MAAK;wCACL,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS;4CACP,iBAAiB;4CACjB,SAAS,WAAW;4CACpB,oBAAoB;wCACtB;wCACA,OAAO;4CACL,YAAY;4CACZ,aAAa;4CACb,WAAW;4CACX,YAAY;4CACZ,UAAU;wCACZ;wCACA,MAAK;kDACN;;;;;;;;;;;;;;;;;sCAOL,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,IAAI;sCACrC,cAAA,2BAAC,UAAI;gCAAC,OAAM;gCAAS,SAAQ;gCAAS,MAAK;0CACzC,cAAA,2BAAC,WAAK;oCAAC,MAAM;oCAAI,IAAI;;sDACnB,2BAAC,aAAO;4CACN,OAAO,CAAC,QAAQ,EAAE,UAAU,iBAAiB,CAAC,CAAC,CAAC;sDAEhD,cAAA,2BAAC,UAAI;gDAAC,OAAM;gDAAS,KAAK;;kEACxB,2BAAC;wDACC,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,YAAY;wDACd;;;;;;kEAEF,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YAAY;4DACZ,OAAO;wDACT;;4DACD;4DACK,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;sDAKrC,2BAAC,aAAO;4CACN,OAAO,CAAC,QAAQ,EAAE,UAAU,mBAAmB,CAAC,CAAC,CAAC;sDAElD,cAAA,2BAAC,UAAI;gDAAC,OAAM;gDAAS,KAAK;;kEACxB,2BAAC;wDACC,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,YAAY;wDACd;;;;;;kEAEF,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YAAY;4DACZ,OAAO;wDACT;;4DACD;4DACK,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;sDAKvC,2BAAC,aAAO;4CACN,OAAO,CAAC,QAAQ,EAAE,UAAU,gBAAgB,CAAC,CAAC,CAAC;sDAE/C,cAAA,2BAAC,UAAI;gDAAC,OAAM;gDAAS,KAAK;;kEACxB,2BAAC;wDACC,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,YAAY;wDACd;;;;;;kEAEF,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,YAAY;4DACZ,OAAO;wDACT;;4DACD;4DACK,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS1C,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,IAAI;sCACrC,cAAA,2BAAC,UAAI;gCAAC,OAAM;gCAAS,SAAQ;0CAC3B,cAAA,2BAAC,aAAO;oCACN,OAAO,CAAC,KAAK,EAAE,UAAU,oBAAoB,CAAC,GAAG,EAAE,UAAU,cAAc,CAAC,CAAC,EAAE,UAAU,UAAU,CAAC,CAAC,CAAC;8CAEtG,cAAA,2BAAC,UAAI;wCAAC,OAAM;wCAAS,KAAK;;0DACxB,2BAAC;gDACC,OAAO;oDAAE,UAAU;oDAAI,YAAY;oDAAK,OAAO;gDAAU;0DAC1D;;;;;;0DAGD,2BAAC,cAAQ;gDACP,SAAS,UAAU,oBAAoB;gDACvC,MAAK;gDACL,OAAO;oDAAE,OAAO;gDAAG;gDACnB,aAAY;gDACZ,UAAU;;;;;;0DAEZ,2BAAC;gDACC,OAAO;oDAAE,UAAU;oDAAI,YAAY;oDAAK,OAAO;gDAAU;;oDAExD,UAAU,oBAAoB;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU9C,2BAAC,UAAI;gBACH,WAAW;gBACX,UAAU,CAAC,MAAQ,aAAa;gBAChC,MAAK;gBACL,OAAO;oBAAE,cAAc;gBAAE;;kCAEzB,2BAAC;wBAAQ,KAAI;uBAAS;;;;;kCACtB,2BAAC;wBAAQ,KAAI;uBAAU;;;;;kCACvB,2BAAC;wBAAQ,KAAI;uBAAU;;;;;;;;;;;YAIxB,sBACC,2BAAC,WAAK;gBACJ,SAAQ;gBACR,aAAa;gBACb,MAAK;gBACL,QAAQ;gBACR,OAAO;oBAAE,cAAc;gBAAG;;;;;qCAG5B,2BAAC,UAAI;gBAAC,UAAU;;kCACd,2BAAC,UAAI;wBACH,YAAY;wBACZ,YAAY,CAAC;4BACX,qBACE,2BAAC,UAAI,CAAC,IAAI;gCACR,WAAU;gCACV,OAAO;oCACL,SAAS;oCACT,cAAc;oCACd,cAAc;oCACd,YAAY;oCACZ,SAAS,KAAK,MAAM,KAAK,IAAI,MAAM;oCACnC,YAAY,CAAC,UAAU,EACrB,KAAK,MAAM,KAAK,IACZ,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,UACT,CAAC;oCACF,WAAW;gCACb;0CAEA,cAAA,2BAAC,UAAI;oCAAC,OAAM;oCAAS,KAAK;oCAAI,OAAO;wCAAE,OAAO;oCAAO;;sDAEnD,2BAAC,UAAI;4CAAC,QAAQ;4CAAC,OAAM;;gDAClB,KAAK,MAAM,KAAK,kBACf,2BAAC,UAAI;oDACH,OAAM;oDACN,SAAQ;oDACR,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,YAAY;oDACd;8DAEA,cAAA,2BAAC,oBAAa;wDACZ,OAAO;4DAAE,OAAO;4DAAQ,UAAU;wDAAG;;;;;;;;;;2EAIzC,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,QAAQ,CAAC,UAAU,EACjB,KAAK,QAAQ,KAAK,IACd,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,UACP,CAAC;oDACJ;;;;;;8DAIJ,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,YAAY;wDACZ,WAAW;oDACb;;;;;;;;;;;;sDAKJ,2BAAC,UAAI;4CAAC,QAAQ;4CAAC,OAAO;gDAAE,MAAM;4CAAE;;8DAC9B,2BAAC;oDACC,OAAO;wDACL,UAAU;wDACV,YAAY,KAAK,QAAQ,KAAK,IAAI,MAAM;wDACxC,gBACE,KAAK,MAAM,KAAK,IAAI,iBAAiB;wDACvC,OAAO,KAAK,MAAM,KAAK,IAAI,YAAY;oDACzC;8DAEC,KAAK,KAAK;;;;;;8DAIb,2BAAC,WAAK;oDAAC,OAAM;oDAAS,MAAM;oDAAG,OAAO;wDAAE,WAAW;oDAAE;;sEACnD,2BAAC,uBAAgB;4DACf,OAAO;gEACL,UAAU;gEACV,OAAO;4DACT;;;;;;sEAEF,2BAAC;4DAAK,MAAK;4DAAY,OAAO;gEAAE,UAAU;4DAAG;;gEAAG;gEACzC;gEACJ,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;sDAMnD,2BAAC,cAAQ;4CACP,SAAS;gDAAC;6CAAQ;4CAClB,MAAM;gDACJ,OAAO;oDACL;wDACE,KAAK;wDACL,OACE,KAAK,MAAM,KAAK,IAAI,UAAU;wDAChC,oBACE,2BAAC,oBAAa;4DACZ,OAAO;gEACL,OACE,KAAK,MAAM,KAAK,IAAI,YAAY;gEAClC,UAAU;4DACZ;;;;;;oDAGN;oDACA;wDACE,KAAK;wDACL,OAAO;wDACP,oBAAM,2BAAC,mBAAY;4DAAC,OAAO;gEAAE,OAAO;4DAAU;;;;;;oDAChD;oDACA;wDACE,KAAK;wDACL,OAAO;wDACP,oBACE,2BAAC,qBAAc;4DAAC,OAAO;gEAAE,OAAO;4DAAU;;;;;;wDAE5C,QAAQ;oDACV;iDACD;gDACD,SAAS,CAAC,EAAE,GAAG,EAAE;oDACf,IAAI,QAAQ,YACV,uBAAuB,KAAK,EAAE;yDACzB,IAAI,QAAQ,QAAQ;wDACzB,iBAAiB,KAAK,EAAE;wDACxB,SAAS,cAAc,CAAC;4DACtB,MAAM,KAAK,KAAK;4DAChB,UAAU,KAAK,QAAQ;wDACzB;wDACA,oBAAoB;oDACtB,OAAO,IAAI,QAAQ,UACjB,iBAAiB,KAAK,EAAE;gDAE5B;4CACF;sDAEA,cAAA,2BAAC,YAAM;gDACL,MAAK;gDACL,MAAK;gDACL,oBAAM,2BAAC,mBAAY;;;;;gDACnB,OAAO;oDAAE,OAAO;oDAAI,QAAQ;gDAAG;;;;;;;;;;;;;;;;;;;;;;wBAM3C;;;;;;kCAIF,2BAAC,WAAK;wBACJ,OAAO,gBAAgB,WAAW;wBAClC,MAAM;wBACN,UAAU;4BACR,oBAAoB;4BACpB,SAAS,WAAW;wBACtB;wBACA,MAAM;4BACJ,SAAS,MAAM;wBACjB;wBACA,QAAQ;wBACR,cAAc;wBACd,QAAQ;0CACN,2BAAC,YAAM;gCAAc,SAAS,IAAM,oBAAoB;0CAAQ;+BAApD;;;;;0CAGZ,2BAAC,YAAM;gCAEL,MAAK;gCACL,SAAS;oCACP,SAAS,MAAM;gCACjB;gCACA,OAAO;oCACL,YAAY;oCACZ,aAAa;oCACb,WAAW;gCACb;0CAEC,gBAAgB,SAAS;+BAXtB;;;;;yBAaP;kCAED,cAAA,2BAAC,UAAI;4BACH,MAAM;4BACN,QAAO;4BACP,UAAU;4BACV,cAAa;;8CAEb,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,2BAAC,WAAK;wCACJ,aAAY;wCACZ,MAAK;wCACL,OAAO;4CAAE,cAAc;wCAAE;;;;;;;;;;;8CAI7B,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,cAAc;oCACd,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAS;qCAAE;8CAE9C,cAAA,2BAAC,YAAM;wCACL,MAAK;wCACL,SAAS;4CACP;gDAAE,OAAO;gDAAG,OAAO;4CAAO;4CAC1B;gDAAE,OAAO;gDAAG,OAAO;4CAAO;4CAC1B;gDAAE,OAAO;gDAAG,OAAO;4CAAO;yCAC3B;wCACD,OAAO;4CAAE,cAAc;wCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C;GA5lBM;;QAgBe,UAAI,CAAC;;;KAhBpB;IA8lBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCzHf;;;eAAA;;;;;;8BA5gBO;6BAWA;wEACoC;6BACf;;;;;;;;;;AAM5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC;;;;;;;;;;;;;;;;;;;;;CAqBC,GACD,MAAM,kBAA4B;;IAChC;;;;;;;;;GASC,GACD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;QAClE,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW;QACX,cAAc;QACd,eAAe;QACf,eAAe;QACf,WAAW;QACX,QAAQ;IACV;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;IAElE;;;;;;;;;;;;;GAaC,GACD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAChD;QACE,UAAU;QACV,WAAW;QACX,UAAU;QACV,QAAQ;IACV;IAEF,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;IAE5D,OAAO;IAEP,SAAS;IACT,IAAA,gBAAS,EAAC;QACR,QAAQ,GAAG,CAAC;QAEZ,MAAM,gBAAgB;YACpB,IAAI;gBACF,QAAQ,GAAG,CAAC;gBAEZ,8BAA8B;gBAC9B,MAAM,oBAAoB,iBAAW,CAAC,oBAAoB,GAAG,KAAK,CAChE,CAAC;oBACC,QAAQ,KAAK,CAAC,eAAe;oBAC7B,iBAAiB;oBACjB,OAAO;gBACT;gBAGF,MAAM,eAAe,iBAAW,CAAC,oBAAoB,GAAG,KAAK,CAC3D,CAAC;oBACC,QAAQ,KAAK,CAAC,aAAa;oBAC3B,cAAc;oBACd,OAAO;gBACT;gBAGF,MAAM,CAAC,YAAY,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAC5C;oBACA;iBACD;gBAED,IAAI,YAAY;oBACd,QAAQ,GAAG,CAAC,+BAA+B;oBAC3C,YAAY;oBACZ,iBAAiB;gBACnB;gBAEA,IAAI,OAAO;oBACT,QAAQ,GAAG,CAAC,6BAA6B;oBACzC,iBAAiB;oBACjB,cAAc;gBAChB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,iBAAiB;gBACjB,cAAc;YAChB,SAAU;gBACR,mBAAmB;gBACnB,gBAAgB;YAClB;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBACE;kBAEG,8BACC,2BAAC,WAAK;YACJ,SAAQ;YACR,aAAa;YACb,MAAK;YACL,QAAQ;YACR,OAAO;gBAAE,cAAc;YAAG;;;;;iCAG5B,2BAAC,UAAI;YAAC,UAAU;sBAEd,cAAA,2BAAC,UAAI;gBACH,OAAO;oBACL,YAAY;oBACZ,cAAc;oBACd,OAAO;oBACP,UAAU;oBACV,UAAU;oBACV,WAAW;oBACX,QAAQ;gBACV;gBACA,QAAQ;oBACN,MAAM;wBACJ,SAAS;wBACT,QAAQ;oBACV;gBACF;;kCAGA,2BAAC;wBACC,OAAO;4BACL,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,cAAc;wBAChB;;;;;;kCAEF,2BAAC;wBACC,OAAO;4BACL,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,cAAc;wBAChB;;;;;;kCAEF,2BAAC;wBACC,OAAO;4BACL,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,cAAc;4BACd,WAAW;wBACb;;;;;;kCAIF,2BAAC,SAAG;wBACF,QAAQ;4BAAC;4BAAI;yBAAG;wBAChB,OAAM;wBACN,OAAO;4BACL,UAAU;4BACV,QAAQ;4BACR,OAAO;4BACP,WAAW;wBACb;;0CAGA,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;gCAAG,IAAI;0CACrC,cAAA,2BAAC,UAAI;oCAAC,OAAM;oCAAS,OAAO;wCAAE,WAAW;oCAAO;;sDAE9C,2BAAC,YAAM;4CACL,MAAM;4CACN,OAAM;4CACN,OAAO;gDACL,iBAAiB;gDACjB,aAAa;gDACb,UAAU;gDACV,YAAY;gDACZ,QAAQ;4CACV;sDAEC,SAAS,IAAI,GACZ,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,mBAEnC,2BAAC,mBAAY;;;;;;;;;;sDAKjB,2BAAC,WAAK;4CAAC,WAAU;4CAAW,MAAM;;8DAChC,2BAAC;oDACC,OAAO;oDACP,OAAO;wDACL,QAAQ;wDACR,OAAO;wDACP,UAAU;wDACV,YAAY;oDACd;8DAEC,SAAS,IAAI,IAAI;;;;;;8DAIpB,2BAAC,WAAK;oDAAC,WAAU;oDAAW,MAAM;;wDAC/B,SAAS,KAAK,kBACb,2BAAC,WAAK;4DAAC,MAAM;4DAAG,OAAM;;8EACpB,2BAAC,mBAAY;oEACX,OAAO;wEACL,UAAU;wEACV,OAAO;oEACT;;;;;;8EAEF,2BAAC;oEACC,OAAO;wEACL,OAAO;wEACP,UAAU;oEACZ;8EAEC,SAAS,KAAK;;;;;;;;;;;;wDAIpB,SAAS,SAAS,kBACjB,2BAAC,WAAK;4DAAC,MAAM;4DAAG,OAAM;;8EACpB,2BAAC,oBAAa;oEACZ,OAAO;wEACL,UAAU;wEACV,OAAO;oEACT;;;;;;8EAEF,2BAAC;oEACC,OAAO;wEACL,OAAO;wEACP,UAAU;oEACZ;8EAEC,SAAS,SAAS;;;;;;;;;;;;;;;;;;gDAO1B,SAAS,YAAY,kBACpB,2BAAC;oDACC,OAAO;wDACL,UAAU;wDACV,OAAO;wDACP,YAAY;oDACd;;wDACD;wDACM,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAQpC,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;gCAAI,IAAI;0CACtC,cAAA,2BAAC,UAAI;oCACH,QAAQ;oCACR,SAAQ;oCACR,OAAO;wCACL,WAAW;wCACX,WAAW;wCACX,SAAS;oCACX;;sDAGA,2BAAC,WAAK;4CACJ,OAAM;4CACN,OAAO;gDACL,gBAAgB;gDAChB,cAAc;4CAChB;;8DAEA,2BAAC,uBAAgB;oDACf,OAAO;wDACL,UAAU;wDACV,OAAO;oDACT;;;;;;8DAEF,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,UAAU;wDACV,YAAY;oDACd;8DACD;;;;;;;;;;;;wCAMF,2BACC,2BAAC;4CACC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAwB;sDACvD;;;;;iEAID,2BAAC,UAAI;4CAAC,UAAU;sDACd,cAAA,2BAAC,SAAG;gDAAC,QAAQ;oDAAC;oDAAG;iDAAE;gDAAE,SAAQ;;kEAC3B,2BAAC,SAAG;wDAAC,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;kEACnC,cAAA,2BAAC;4DAAI,OAAO;gEAAE,WAAW;4DAAS;;8EAChC,2BAAC;oEACC,OAAO;wEACL,UAAU;wEACV,YAAY;wEACZ,OAAO;wEACP,YAAY;oEACd;8EAEC,cAAc,QAAQ;;;;;;8EAEzB,2BAAC;oEACC,OAAO;wEACL,UAAU;wEACV,OAAO;wEACP,WAAW;oEACb;8EACD;;;;;;;;;;;;;;;;;kEAKL,2BAAC,SAAG;wDAAC,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;kEACnC,cAAA,2BAAC;4DAAI,OAAO;gEAAE,WAAW;4DAAS;;8EAChC,2BAAC;oEACC,OAAO;wEACL,UAAU;wEACV,YAAY;wEACZ,OAAO;wEACP,YAAY;oEACd;8EAEC,cAAc,SAAS;;;;;;8EAE1B,2BAAC;oEACC,OAAO;wEACL,UAAU;wEACV,OAAO;wEACP,WAAW;oEACb;8EACD;;;;;;;;;;;;;;;;;kEAKL,2BAAC,SAAG;wDAAC,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;kEACnC,cAAA,2BAAC;4DAAI,OAAO;gEAAE,WAAW;4DAAS;;8EAChC,2BAAC;oEACC,OAAO;wEACL,UAAU;wEACV,YAAY;wEACZ,OAAO;wEACP,YAAY;oEACd;8EAEC,cAAc,QAAQ;;;;;;8EAEzB,2BAAC;oEACC,OAAO;wEACL,UAAU;wEACV,OAAO;wEACP,WAAW;oEACb;8EACD;;;;;;;;;;;;;;;;;kEAKL,2BAAC,SAAG;wDAAC,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;kEACnC,cAAA,2BAAC;4DAAI,OAAO;gEAAE,WAAW;4DAAS;;8EAChC,2BAAC;oEACC,OAAO;wEACL,UAAU;wEACV,YAAY;wEACZ,OAAO;wEACP,YAAY;oEACd;8EAEC,cAAc,MAAM;;;;;;8EAEvB,2BAAC;oEACC,OAAO;wEACL,UAAU;wEACV,OAAO;wEACP,WAAW;oEACb;8EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAYf,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;gCAAG,IAAI;0CACrC,cAAA,2BAAC,UAAI;oCACH,QAAQ;oCACR,SAAQ;oCACR,OAAO;wCAAE,WAAW;wCAAQ,SAAS;oCAAQ;8CAE7C,cAAA,2BAAC,WAAK;wCAAC,WAAU;wCAAW,MAAM;;0DAChC,2BAAC,WAAK;gDAAC,WAAU;gDAAW,MAAM;;kEAChC,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,OAAO;4DACP,YAAY;wDACd;kEACD;;;;;;kEAGD,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,OAAO;4DACP,YAAY;4DACZ,YAAY;wDACd;kEAEC,SAAS,aAAa,IAAI;;;;;;;;;;;;0DAG/B,2BAAC,WAAK;gDAAC,WAAU;gDAAW,MAAM;;kEAChC,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,OAAO;4DACP,YAAY;wDACd;kEACD;;;;;;kEAGD,2BAAC;wDACC,OAAO;4DACL,UAAU;4DACV,OAAO;4DACP,YAAY;4DACZ,YAAY;wDACd;kEAEC,SAAS,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnD;GA/dM;KAAA;IAieN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCpVf;;;eAAA;;;;;;;4BA7LyB;6BACY;uEACnB;6EACU;8EACH;gFACE;iFACC;;;;;;;;;;AAE5B;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC,GACD,MAAM,qBAA+B;;IACnC;;;;;;GAMC,GACD,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,IAAA,aAAQ,EAAC;IAI3C;;;;;GAKC,GACD,IAAI,SACF,qBACE,2BAAC;QACC,OAAO;YACL,WAAW;YACX,YAAY;YACZ,SAAS;YACT,gBAAgB;YAChB,YAAY;QACd;;0BAEA,2BAAC,UAAI;gBAAC,MAAK;;;;;;0BACX,2BAAC;gBAAI,OAAO;oBAAE,YAAY;gBAAG;0BAAG;;;;;;;;;;;;IAKtC;;;;;;;;;GASC,GAED,qBACE;;0BAEE,2BAAC;gBACC,OAAO;oBACL,WAAW;oBACX,YAAY;oBACZ,SAAS;gBACX;0BAWA,cAAA,2BAAC,UAAI;oBACH,OAAO;wBACL,OAAO;wBACP,WAAW;wBACX,cAAc;wBACd,WAAW;oBACb;oBACA,QAAQ;wBACN,MAAM;4BACJ,SAAS;wBACX;oBACF;8BAWA,cAAA,2BAAC,SAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;wBAAE,OAAO;4BAAE,QAAQ;wBAAE;;0CAOxC,2BAAC,SAAG;gCAAC,IAAI;gCAAI,OAAO;oCAAE,cAAc;gCAAE;0CACpC,cAAA,2BAAC,wBAAe;;;;;;;;;;0CAWlB,2BAAC,SAAG;gCACF,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,KAAK;gCACL,OAAO;oCAAE,cAAc;gCAAE;0CAEzB,cAAA,2BAAC,uBAAc;;;;;;;;;;0CAYjB,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,KAAK;0CAChD,cAAA,2BAAC,qBAAY;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBrB,2BAAC,oBAAe;;;;;;;AAOtB;GAvJM;;QAQ8B,aAAQ;;;KARtC;IAyJN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7Lf;;CAEC;;;;4BAUY;;;eAAA;;;;;gCAFc;;;;;;;;;AAEpB,MAAM;IACX;;GAEC,GACD,aAAa,eAAwC;QACnD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAiB;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WAAW,OAA0B,EAAyB;QACzE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAe,UAAU;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WACX,EAAU,EACV,OAA0B,EACH;QACvB,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,CAAC,OAAO,EAAE,GAAG,CAAC,EACd;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WAAW,EAAU,EAAiB;QACjD,MAAM,mBAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC;IACxC;IAEA;;GAEC,GACD,aAAa,eAA2C;QACtD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAoB;QACzD,OAAO,SAAS,IAAI;IACtB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;ACzDA;;;CAGC,GAED,eAAe;;;;;;;;;;;;IAmDF,6BAA6B;eAA7B;;IAaA,uBAAuB;eAAvB;;IAxDA,2BAA2B;eAA3B;;IAkCA,mBAAmB;eAAnB;;IAjBA,mBAAmB;eAAnB;;;;;;;;;;;;;AAxBb,MAAM,qBAAqB;AAOpB,MAAM,8BAA8B,CAAC;IAC1C,IAAI;QACF,MAAM,UAAU,aAAa,OAAO,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC;QACtE,IAAI,SACF,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC;IAE9B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;IAC/B;IACA,OAAO,IAAI;AACb;AAOO,MAAM,sBAAsB,CAAC,QAAgB;IAClD,IAAI;QACF,MAAM,UAAU,4BAA4B;QAC5C,QAAQ,GAAG,CAAC;QACZ,aAAa,OAAO,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,SAAS,CAAC;eAAI;SAAQ;QACnF,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,KAAK,EAAE,OAAO,CAAC;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;IAC/B;AACF;AAQO,MAAM,sBAAsB,CAAC,QAAgB;IAClD,MAAM,UAAU,4BAA4B;IAC5C,OAAO,QAAQ,GAAG,CAAC;AACrB;AAMO,MAAM,gCAAgC,CAAC;IAC5C,IAAI;QACF,aAAa,UAAU,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC;QACzD,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,OAAO,CAAC;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;IAC/B;AACF;AAMO,MAAM,0BAA0B;IACrC,MAAM,OAAiB,EAAE;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;QAC7B,IAAI,OAAO,IAAI,UAAU,CAAC,qBACxB,KAAK,IAAI,CAAC;IAEd;IACA,OAAO;AACT"}