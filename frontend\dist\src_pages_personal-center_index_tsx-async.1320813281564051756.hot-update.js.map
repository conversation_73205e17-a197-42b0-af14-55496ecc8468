{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.1320813281564051756.hot-update.js", "src/pages/personal-center/PersonalInfo.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='15712615307817413930';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/team-management/index.tsx\":[\"src/pages/team-management/index.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/services/invitation.ts\":[\"src/services/invitation.ts\"]});;\r\n  },\r\n);\r\n", "import {\n  ClockCircleOutlined,\n  MailOutlined,\n  PhoneOutlined,\n  PlusOutlined,\n  SettingOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Avatar,\n  Button,\n  Card,\n  Col,\n  Dropdown,\n  Flex,\n  Row,\n  Space,\n  Spin,\n  Typography,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserProfileDetailResponse } from '@/types/user';\nimport CreateTeamModal from './CreateTeamModal';\nimport PersonalSettingsModal from './PersonalSettingsModal';\n\nconst { Title, Text } = Typography;\n\n/**\n * 个人信息组件\n *\n * 显示用户的完整个人信息，包括基本信息和登录历史。\n * 整合了原UserProfileCard和LastLoginInfo组件的功能。\n *\n * 主要功能：\n * 1. 显示用户头像（使用姓名首字母）\n * 2. 显示用户姓名、邮箱、电话\n * 3. 显示注册日期\n * 4. 显示最后登录时间和登录团队\n *\n * 数据来源：\n * - 用户详细信息：通过UserService.getUserProfileDetail()获取\n */\nconst PersonalInfo: React.FC = () => {\n  /**\n   * 用户详细信息状态管理\n   */\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\n    name: '',\n    position: '',\n    email: '',\n    telephone: '',\n    registerDate: '',\n    lastLoginTime: '',\n    lastLoginTeam: '',\n    teamCount: 0,\n    avatar: '',\n  });\n\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\n\n  // Modal状态管理\n  const [createTeamModalVisible, setCreateTeamModalVisible] = useState(false);\n  const [personalSettingsModalVisible, setPersonalSettingsModalVisible] = useState(false);\n\n  // 获取用户数据\n  useEffect(() => {\n    const fetchUserData = async () => {\n      try {\n        const userDetail = await UserService.getUserProfileDetail();\n        setUserInfo(userDetail);\n        setUserInfoError(null);\n      } catch (error) {\n        console.error('获取用户详细信息失败:', error);\n        setUserInfoError('获取用户详细信息失败，请稍后重试');\n      } finally {\n        setUserInfoLoading(false);\n      }\n    };\n\n    fetchUserData();\n  }, []);\n\n  return (\n    <Card\n      title=\"个人信息\"\n      style={{\n        marginBottom: 16,\n        borderRadius: 8,\n      }}\n      styles={{\n        header: {\n          borderBottom: '1px solid #f0f0f0',\n          paddingBottom: 12,\n        },\n        body: {\n          padding: '16px',\n        },\n      }}\n    >\n      {userInfoError ? (\n        <Alert\n          message=\"个人信息加载失败\"\n          description={userInfoError}\n          type=\"error\"\n          showIcon\n        />\n      ) : (\n        <Spin spinning={userInfoLoading}>\n          {/* 明信片式紧凑布局 */}\n          <div style={{ padding: '16px' }}>\n            <Row gutter={[16, 0]} align=\"middle\">\n              {/* 左侧：头像区域 */}\n              <Col xs={24} sm={6} md={5} lg={4} xl={4}>\n                <Flex justify=\"center\" align=\"center\" style={{ height: '100%' }}>\n                  <Avatar\n                    size={56}\n                    shape=\"circle\"\n                    style={{\n                      backgroundColor: '#1890ff',\n                      fontSize: 20,\n                      fontWeight: 600,\n                      border: '2px solid #e6f4ff',\n                      boxShadow: '0 2px 8px rgba(24, 144, 255, 0.12)',\n                    }}\n                  >\n                    {userInfo.name ? (\n                      userInfo.name.charAt(0).toUpperCase()\n                    ) : (\n                      <UserOutlined />\n                    )}\n                  </Avatar>\n                </Flex>\n              </Col>\n\n              {/* 右侧：用户信息区域 */}\n              <Col xs={24} sm={18} md={19} lg={20} xl={20}>\n                <div style={{ paddingLeft: '12px' }}>\n                  {/* 第一行：姓名和操作按钮 */}\n                  <div style={{ marginBottom: '8px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <Title\n                      level={4}\n                      style={{\n                        margin: 0,\n                        fontSize: 18,\n                        fontWeight: 600,\n                        color: '#262626',\n                        lineHeight: 1.3,\n                      }}\n                    >\n                      {userInfo.name || '加载中...'}\n                    </Title>\n\n                    {/* 操作按钮区域 */}\n                    <Space size={8}>\n                      <Button\n                        type=\"primary\"\n                        size=\"small\"\n                        icon={<PlusOutlined />}\n                        onClick={() => setCreateTeamModalVisible(true)}\n                        style={{\n                          borderRadius: 6,\n                          fontSize: 12,\n                          height: 28,\n                          padding: '0 12px',\n                        }}\n                      >\n                        新建团队\n                      </Button>\n                      <Button\n                        size=\"small\"\n                        icon={<SettingOutlined />}\n                        onClick={() => setPersonalSettingsModalVisible(true)}\n                        style={{\n                          borderRadius: 6,\n                          fontSize: 12,\n                          height: 28,\n                          padding: '0 12px',\n                          border: '1px solid #d9d9d9',\n                        }}\n                      >\n                        个人设置\n                      </Button>\n                    </Space>\n                  </div>\n\n                  {/* 第二行：联系信息 */}\n                  <div style={{ marginBottom: '8px' }}>\n                    <Space size={16} wrap>\n                      {userInfo.email && (\n                        <Space size={6} align=\"center\">\n                          <MailOutlined\n                            style={{\n                              fontSize: 14,\n                              color: '#1890ff',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              color: '#595959',\n                              fontSize: 13,\n                              fontWeight: 500,\n                            }}\n                          >\n                            {userInfo.email}\n                          </Text>\n                        </Space>\n                      )}\n                      {userInfo.telephone && (\n                        <Space size={6} align=\"center\">\n                          <PhoneOutlined\n                            style={{\n                              fontSize: 14,\n                              color: '#52c41a',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              color: '#595959',\n                              fontSize: 13,\n                              fontWeight: 500,\n                            }}\n                          >\n                            {userInfo.telephone}\n                          </Text>\n                        </Space>\n                      )}\n                    </Space>\n                  </div>\n\n                  {/* 第三行：注册日期和登录信息 */}\n                  <div>\n                    <Space size={16} wrap>\n                      {/* 注册日期 */}\n                      {userInfo.registerDate && (\n                        <Space size={4} align=\"center\">\n                          <Text\n                            style={{\n                              fontSize: 12,\n                              color: '#8c8c8c',\n                              fontWeight: 500,\n                            }}\n                          >\n                            📅 注册于 {userInfo.registerDate}\n                          </Text>\n                        </Space>\n                      )}\n\n                      {/* 最后登录时间 */}\n                      {userInfo.lastLoginTime && (\n                        <Space size={4} align=\"center\">\n                          <ClockCircleOutlined\n                            style={{\n                              fontSize: 12,\n                              color: '#1890ff',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              fontSize: 12,\n                              color: '#8c8c8c',\n                              fontWeight: 500,\n                            }}\n                          >\n                            最后登录：{userInfo.lastLoginTime}\n                          </Text>\n                        </Space>\n                      )}\n\n                      {/* 最后登录团队 */}\n                      {userInfo.lastLoginTeam && (\n                        <Space size={4} align=\"center\">\n                          <TeamOutlined\n                            style={{\n                              fontSize: 12,\n                              color: '#52c41a',\n                            }}\n                          />\n                          <Text\n                            style={{\n                              fontSize: 12,\n                              color: '#8c8c8c',\n                              fontWeight: 500,\n                            }}\n                          >\n                            团队：{userInfo.lastLoginTeam}\n                          </Text>\n                        </Space>\n                      )}\n                    </Space>\n                  </div>\n                </div>\n              </Col>\n            </Row>\n          </div>\n        </Spin>\n      )}\n    </Card>\n  );\n};\n\nexport default PersonalInfo;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCC6Sb;;;2BAAA;;;;;;0CAxSO;yCAaA;oFACoC;yCACf;;;;;;;;;;YAK5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAElC;;;;;;;;;;;;;;CAcC,GACD,MAAM,eAAyB;;gBAC7B;;GAEC,GACD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;oBAClE,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,WAAW;oBACX,QAAQ;gBACV;gBAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;gBACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,YAAY;gBACZ,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,IAAA,eAAQ,EAAC;gBACrE,MAAM,CAAC,8BAA8B,gCAAgC,GAAG,IAAA,eAAQ,EAAC;gBAEjF,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,gBAAgB;wBACpB,IAAI;4BACF,MAAM,aAAa,MAAM,iBAAW,CAAC,oBAAoB;4BACzD,YAAY;4BACZ,iBAAiB;wBACnB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,eAAe;4BAC7B,iBAAiB;wBACnB,SAAU;4BACR,mBAAmB;wBACrB;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,qBACE,2BAAC,UAAI;oBACH,OAAM;oBACN,OAAO;wBACL,cAAc;wBACd,cAAc;oBAChB;oBACA,QAAQ;wBACN,QAAQ;4BACN,cAAc;4BACd,eAAe;wBACjB;wBACA,MAAM;4BACJ,SAAS;wBACX;oBACF;8BAEC,8BACC,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,QAAQ;;;;;6CAGV,2BAAC,UAAI;wBAAC,UAAU;kCAEd,cAAA,2BAAC;4BAAI,OAAO;gCAAE,SAAS;4BAAO;sCAC5B,cAAA,2BAAC,SAAG;gCAAC,QAAQ;oCAAC;oCAAI;iCAAE;gCAAE,OAAM;;kDAE1B,2BAAC,SAAG;wCAAC,IAAI;wCAAI,IAAI;wCAAG,IAAI;wCAAG,IAAI;wCAAG,IAAI;kDACpC,cAAA,2BAAC,UAAI;4CAAC,SAAQ;4CAAS,OAAM;4CAAS,OAAO;gDAAE,QAAQ;4CAAO;sDAC5D,cAAA,2BAAC,YAAM;gDACL,MAAM;gDACN,OAAM;gDACN,OAAO;oDACL,iBAAiB;oDACjB,UAAU;oDACV,YAAY;oDACZ,QAAQ;oDACR,WAAW;gDACb;0DAEC,SAAS,IAAI,GACZ,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,mBAEnC,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;kDAOrB,2BAAC,SAAG;wCAAC,IAAI;wCAAI,IAAI;wCAAI,IAAI;wCAAI,IAAI;wCAAI,IAAI;kDACvC,cAAA,2BAAC;4CAAI,OAAO;gDAAE,aAAa;4CAAO;;8DAEhC,2BAAC;oDAAI,OAAO;wDAAE,cAAc;wDAAO,SAAS;wDAAQ,gBAAgB;wDAAiB,YAAY;oDAAS;;sEACxG,2BAAC;4DACC,OAAO;4DACP,OAAO;gEACL,QAAQ;gEACR,UAAU;gEACV,YAAY;gEACZ,OAAO;gEACP,YAAY;4DACd;sEAEC,SAAS,IAAI,IAAI;;;;;;sEAIpB,2BAAC,WAAK;4DAAC,MAAM;;8EACX,2BAAC,YAAM;oEACL,MAAK;oEACL,MAAK;oEACL,oBAAM,2BAAC,mBAAY;;;;;oEACnB,SAAS,IAAM,0BAA0B;oEACzC,OAAO;wEACL,cAAc;wEACd,UAAU;wEACV,QAAQ;wEACR,SAAS;oEACX;8EACD;;;;;;8EAGD,2BAAC,YAAM;oEACL,MAAK;oEACL,oBAAM,2BAAC,sBAAe;;;;;oEACtB,SAAS,IAAM,gCAAgC;oEAC/C,OAAO;wEACL,cAAc;wEACd,UAAU;wEACV,QAAQ;wEACR,SAAS;wEACT,QAAQ;oEACV;8EACD;;;;;;;;;;;;;;;;;;8DAOL,2BAAC;oDAAI,OAAO;wDAAE,cAAc;oDAAM;8DAChC,cAAA,2BAAC,WAAK;wDAAC,MAAM;wDAAI,IAAI;;4DAClB,SAAS,KAAK,kBACb,2BAAC,WAAK;gEAAC,MAAM;gEAAG,OAAM;;kFACpB,2BAAC,mBAAY;wEACX,OAAO;4EACL,UAAU;4EACV,OAAO;wEACT;;;;;;kFAEF,2BAAC;wEACC,OAAO;4EACL,OAAO;4EACP,UAAU;4EACV,YAAY;wEACd;kFAEC,SAAS,KAAK;;;;;;;;;;;;4DAIpB,SAAS,SAAS,kBACjB,2BAAC,WAAK;gEAAC,MAAM;gEAAG,OAAM;;kFACpB,2BAAC,oBAAa;wEACZ,OAAO;4EACL,UAAU;4EACV,OAAO;wEACT;;;;;;kFAEF,2BAAC;wEACC,OAAO;4EACL,OAAO;4EACP,UAAU;4EACV,YAAY;wEACd;kFAEC,SAAS,SAAS;;;;;;;;;;;;;;;;;;;;;;;8DAQ7B,2BAAC;8DACC,cAAA,2BAAC,WAAK;wDAAC,MAAM;wDAAI,IAAI;;4DAElB,SAAS,YAAY,kBACpB,2BAAC,WAAK;gEAAC,MAAM;gEAAG,OAAM;0EACpB,cAAA,2BAAC;oEACC,OAAO;wEACL,UAAU;wEACV,OAAO;wEACP,YAAY;oEACd;;wEACD;wEACS,SAAS,YAAY;;;;;;;;;;;;4DAMlC,SAAS,aAAa,kBACrB,2BAAC,WAAK;gEAAC,MAAM;gEAAG,OAAM;;kFACpB,2BAAC,0BAAmB;wEAClB,OAAO;4EACL,UAAU;4EACV,OAAO;wEACT;;;;;;kFAEF,2BAAC;wEACC,OAAO;4EACL,UAAU;4EACV,OAAO;4EACP,YAAY;wEACd;;4EACD;4EACO,SAAS,aAAa;;;;;;;;;;;;;4DAMjC,SAAS,aAAa,kBACrB,2BAAC,WAAK;gEAAC,MAAM;gEAAG,OAAM;;kFACpB,2BAAC,mBAAY;wEACX,OAAO;4EACL,UAAU;4EACV,OAAO;wEACT;;;;;;kFAEF,2BAAC;wEACC,OAAO;4EACL,UAAU;4EACV,OAAO;4EACP,YAAY;wEACd;;4EACD;4EACK,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YActD;eAjQM;iBAAA;gBAmQN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID7SD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,8BAA6B;YAAC;SAA6B;IAAA;;AAC/rB"}