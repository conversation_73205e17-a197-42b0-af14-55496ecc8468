import { SettingOutlined, UserOutlined } from '@ant-design/icons';
import {
  Avatar,
  Form,
  Input,
  Modal,
  Space,
  Typography,
  Upload,
  message,
} from 'antd';
import React, { useEffect } from 'react';
import type { UserProfileDetailResponse } from '@/types/user';

const { Title, Text } = Typography;

interface PersonalSettingsModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess?: () => void;
  userInfo: UserProfileDetailResponse;
}

/**
 * 个人设置Modal组件
 *
 * 提供编辑个人信息的界面，包含用户基本信息的修改功能。
 *
 * 主要功能：
 * 1. 编辑用户姓名
 * 2. 编辑联系方式（邮箱、电话）
 * 3. 头像上传（预留功能）
 * 4. 表单验证和提交
 *
 * Props:
 * - visible: 控制Modal显示/隐藏
 * - onCancel: 取消操作回调
 * - onSuccess: 保存成功回调
 * - userInfo: 当前用户信息
 */
const PersonalSettingsModal: React.FC<PersonalSettingsModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  userInfo,
}) => {
  const [form] = Form.useForm();

  // 当Modal打开时，填充表单数据
  useEffect(() => {
    if (visible && userInfo) {
      form.setFieldsValue({
        name: userInfo.name,
        email: userInfo.email,
        telephone: userInfo.telephone,
        position: userInfo.position,
      });
    }
  }, [visible, userInfo, form]);

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('更新个人信息:', values);
      
      // TODO: 调用更新用户信息的API
      // await UserService.updateUserProfile(values);
      
      message.success('个人信息更新成功！');
      onSuccess?.();
      onCancel();
    } catch (error) {
      console.error('更新个人信息失败:', error);
      message.error('更新个人信息失败，请稍后重试');
    }
  };

  // 处理取消操作
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={
        <Space align="center">
          <SettingOutlined style={{ fontSize: 18, color: '#1890ff' }} />
          <Title level={4} style={{ margin: 0, fontSize: 16 }}>
            个人设置
          </Title>
        </Space>
      }
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      okText="保存设置"
      cancelText="取消"
      width={520}
      destroyOnClose
      styles={{
        header: {
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: 16,
        },
        body: {
          padding: '24px',
        },
      }}
    >
      <div style={{ marginBottom: 16 }}>
        <Text style={{ color: '#8c8c8c', fontSize: 14 }}>
          编辑您的个人信息和偏好设置
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        requiredMark={false}
        autoComplete="off"
      >
        {/* 头像区域 */}
        <Form.Item
          label={
            <Text style={{ fontWeight: 600, fontSize: 14 }}>
              头像
            </Text>
          }
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Avatar
              size={64}
              shape="circle"
              style={{
                backgroundColor: '#1890ff',
                fontSize: 24,
                fontWeight: 600,
                border: '2px solid #e6f4ff',
              }}
            >
              {userInfo.name ? (
                userInfo.name.charAt(0).toUpperCase()
              ) : (
                <UserOutlined />
              )}
            </Avatar>
            <div>
              <Text style={{ fontSize: 14, color: '#8c8c8c' }}>
                点击上传新头像（功能开发中）
              </Text>
            </div>
          </div>
        </Form.Item>

        {/* 姓名 */}
        <Form.Item
          label={
            <Text style={{ fontWeight: 600, fontSize: 14 }}>
              姓名
            </Text>
          }
          name="name"
          rules={[
            { required: true, message: '请输入姓名' },
            { min: 2, message: '姓名至少2个字符' },
            { max: 20, message: '姓名不能超过20个字符' },
          ]}
        >
          <Input
            placeholder="请输入姓名"
            style={{
              borderRadius: 6,
              fontSize: 14,
            }}
          />
        </Form.Item>

        {/* 职位 */}
        <Form.Item
          label={
            <Text style={{ fontWeight: 600, fontSize: 14 }}>
              职位
            </Text>
          }
          name="position"
          rules={[
            { max: 50, message: '职位不能超过50个字符' },
          ]}
        >
          <Input
            placeholder="请输入职位（可选）"
            style={{
              borderRadius: 6,
              fontSize: 14,
            }}
          />
        </Form.Item>

        {/* 邮箱 */}
        <Form.Item
          label={
            <Text style={{ fontWeight: 600, fontSize: 14 }}>
              邮箱
            </Text>
          }
          name="email"
          rules={[
            { required: true, message: '请输入邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' },
          ]}
        >
          <Input
            placeholder="请输入邮箱"
            style={{
              borderRadius: 6,
              fontSize: 14,
            }}
          />
        </Form.Item>

        {/* 电话 */}
        <Form.Item
          label={
            <Text style={{ fontWeight: 600, fontSize: 14 }}>
              电话
            </Text>
          }
          name="telephone"
          rules={[
            { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' },
          ]}
        >
          <Input
            placeholder="请输入电话（可选）"
            style={{
              borderRadius: 6,
              fontSize: 14,
            }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PersonalSettingsModal;
