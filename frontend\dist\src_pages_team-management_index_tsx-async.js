((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['src/pages/team-management/index.tsx'],
{ "src/components/InvitationStatus.tsx": function (module, exports, __mako_require__){
/**
 * 邀请状态显示组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _api = __mako_require__("src/types/api.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
/**
 * 邀请状态组件
 */ const InvitationStatusComponent = ({ status, isExpired = false })=>{
    // 如果已过期，优先显示过期状态
    if (isExpired && status === _api.InvitationStatus.PENDING) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
            fileName: "src/components/InvitationStatus.tsx",
            lineNumber: 31,
            columnNumber: 18
        }, void 0),
        color: "orange",
        children: "已过期"
    }, void 0, false, {
        fileName: "src/components/InvitationStatus.tsx",
        lineNumber: 31,
        columnNumber: 7
    }, this);
    switch(status){
        case _api.InvitationStatus.PENDING:
            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {}, void 0, false, {
                    fileName: "src/components/InvitationStatus.tsx",
                    lineNumber: 40,
                    columnNumber: 20
                }, void 0),
                color: "blue",
                children: "待确认"
            }, void 0, false, {
                fileName: "src/components/InvitationStatus.tsx",
                lineNumber: 40,
                columnNumber: 9
            }, this);
        case _api.InvitationStatus.ACCEPTED:
            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {}, void 0, false, {
                    fileName: "src/components/InvitationStatus.tsx",
                    lineNumber: 46,
                    columnNumber: 20
                }, void 0),
                color: "green",
                children: "已接受"
            }, void 0, false, {
                fileName: "src/components/InvitationStatus.tsx",
                lineNumber: 46,
                columnNumber: 9
            }, this);
        case _api.InvitationStatus.REJECTED:
            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CloseCircleOutlined, {}, void 0, false, {
                    fileName: "src/components/InvitationStatus.tsx",
                    lineNumber: 52,
                    columnNumber: 20
                }, void 0),
                color: "red",
                children: "已拒绝"
            }, void 0, false, {
                fileName: "src/components/InvitationStatus.tsx",
                lineNumber: 52,
                columnNumber: 9
            }, this);
        case _api.InvitationStatus.EXPIRED:
            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                    fileName: "src/components/InvitationStatus.tsx",
                    lineNumber: 58,
                    columnNumber: 20
                }, void 0),
                color: "orange",
                children: "已过期"
            }, void 0, false, {
                fileName: "src/components/InvitationStatus.tsx",
                lineNumber: 58,
                columnNumber: 9
            }, this);
        case _api.InvitationStatus.CANCELLED:
            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.StopOutlined, {}, void 0, false, {
                    fileName: "src/components/InvitationStatus.tsx",
                    lineNumber: 64,
                    columnNumber: 20
                }, void 0),
                color: "default",
                children: "已取消"
            }, void 0, false, {
                fileName: "src/components/InvitationStatus.tsx",
                lineNumber: 64,
                columnNumber: 9
            }, this);
        default:
            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                color: "default",
                children: "未知状态"
            }, void 0, false, {
                fileName: "src/components/InvitationStatus.tsx",
                lineNumber: 70,
                columnNumber: 9
            }, this);
    }
};
_c = InvitationStatusComponent;
var _default = InvitationStatusComponent;
var _c;
$RefreshReg$(_c, "InvitationStatusComponent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team-management/components/TeamMemberManagement.tsx": function (module, exports, __mako_require__){
/**
 * 团队成员与邀请管理组件
 *
 * 功能特性：
 * - 统一页面显示团队成员列表和邀请记录
 * - 查看团队成员列表及详细信息
 * - 查看团队邀请列表及状态管理
 * - 添加新成员（通过邮箱邀请）
 * - 移除团队现有成员
 * - 取消待处理的邀请
 * - 批量操作支持
 * - 成员和邀请搜索筛选
 *
 * 权限控制：
 * - 只有团队创建者可以进行成员管理操作
 * - 创建者不能移除自己
 * - 提供详细的操作确认
 *
 * 界面设计：
 * - 移除标签页导航，采用统一页面布局
 * - 团队成员和邀请记录垂直排列
 * - 提升用户体验和操作效率
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _dayjs = _interop_require_default._(__mako_require__("node_modules/dayjs/dayjs.min.js"));
var _team = __mako_require__("src/services/team.ts");
var _invitation = __mako_require__("src/services/invitation.ts");
var _api = __mako_require__("src/types/api.ts");
var _InvitationStatus = _interop_require_default._(__mako_require__("src/components/InvitationStatus.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const TeamMemberManagement = ({ teamDetail, onRefresh })=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [members, setMembers] = (0, _react.useState)([]);
    const [searchText, setSearchText] = (0, _react.useState)('');
    const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
    const [invitations, setInvitations] = (0, _react.useState)([]);
    const [invitationLoading, setInvitationLoading] = (0, _react.useState)(false);
    const [invitationSearchText, setInvitationSearchText] = (0, _react.useState)('');
    const [statusFilter, setStatusFilter] = (0, _react.useState)('');
    (0, _react.useEffect)(()=>{
        fetchMembers();
        fetchInvitations();
    }, []);
    const fetchMembers = async ()=>{
        try {
            setLoading(true);
            const memberList = await _team.TeamService.getCurrentTeamMembers();
            setMembers(memberList || []);
        } catch (error) {
            setMembers([]);
        } finally{
            setLoading(false);
        }
    };
    const fetchInvitations = async ()=>{
        try {
            setInvitationLoading(true);
            const invitationList = await _invitation.InvitationService.getCurrentTeamInvitations();
            setInvitations(invitationList || []);
        } catch (error) {
            setInvitations([]);
        } finally{
            setInvitationLoading(false);
        }
    };
    const handleCancelInvitation = async (invitationId)=>{
        try {
            await _invitation.InvitationService.cancelInvitation(invitationId);
            fetchInvitations();
            onRefresh();
        } catch (error) {}
    };
    const handleRemoveMember = async (member)=>{
        try {
            await _team.TeamService.removeMember(member.id);
            fetchMembers();
            onRefresh();
        } catch (error) {}
    };
    const handleBatchRemove = async ()=>{
        try {
            const memberIds = selectedRowKeys;
            for (const memberId of memberIds)await _team.TeamService.removeMember(memberId);
            setSelectedRowKeys([]);
            fetchMembers();
            onRefresh();
        } catch (error) {}
    };
    const filteredMembers = (members || []).filter((member)=>member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase()));
    const handleToggleMemberStatus = async (member, isActive)=>{
        try {
            await _team.TeamService.updateMemberStatus(member.id, isActive);
            fetchMembers();
            onRefresh();
        } catch (error) {}
    };
    const columns = [
        {
            title: '邮箱',
            dataIndex: 'email',
            key: 'email',
            render: (email)=>(0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: email
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 173,
                    columnNumber: 9
                }, this)
        },
        {
            title: '状态',
            dataIndex: 'isActive',
            key: 'status',
            width: 100,
            render: (isActive)=>(0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: isActive ? 'green' : 'red',
                    children: isActive ? '启用' : '停用'
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 182,
                    columnNumber: 9
                }, this)
        },
        {
            title: '加入时间',
            dataIndex: 'assignedAt',
            key: 'assignedAt',
            render: (date)=>new Date(date).toLocaleDateString()
        },
        {
            title: '最后访问',
            dataIndex: 'lastAccessTime',
            key: 'lastAccessTime',
            render: (date)=>new Date(date).toLocaleDateString()
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record)=>{
                if (record.isCreator) return (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: "-"
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 204,
                    columnNumber: 18
                }, this);
                return (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        record.isActive ? (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "text",
                            size: "small",
                            onClick: ()=>handleToggleMemberStatus(record, false),
                            children: "停用"
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 210,
                            columnNumber: 15
                        }, this) : (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "text",
                            size: "small",
                            onClick: ()=>handleToggleMemberStatus(record, true),
                            children: "启用"
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 218,
                            columnNumber: 15
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                            title: "确认移除成员",
                            description: `确定要移除成员 ${record.name} 吗？此操作不可恢复。`,
                            onConfirm: ()=>handleRemoveMember(record),
                            okText: "确认",
                            cancelText: "取消",
                            okType: "danger",
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                danger: true,
                                size: "small",
                                icon: (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 238,
                                    columnNumber: 23
                                }, void 0),
                                children: "移除"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 234,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 226,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 208,
                    columnNumber: 11
                }, this);
            }
        }
    ];
    const rowSelection = {
        selectedRowKeys,
        onChange: setSelectedRowKeys,
        getCheckboxProps: (record)=>({
                disabled: record.isCreator
            })
    };
    const invitationColumns = [
        {
            title: '受邀人员',
            key: 'invitee',
            render: (_, record)=>(0, _jsxdevruntime.jsxDEV)(Text, {
                    children: record.inviteeEmail
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 264,
                    columnNumber: 9
                }, this)
        },
        {
            title: '邀请状态',
            dataIndex: 'status',
            key: 'status',
            render: (status, record)=>(0, _jsxdevruntime.jsxDEV)(_InvitationStatus.default, {
                    status: status,
                    isExpired: record.isExpired
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 272,
                    columnNumber: 9
                }, this),
            filters: [
                {
                    text: '待确认',
                    value: _api.InvitationStatus.PENDING
                },
                {
                    text: '已接受',
                    value: _api.InvitationStatus.ACCEPTED
                },
                {
                    text: '已拒绝',
                    value: _api.InvitationStatus.REJECTED
                },
                {
                    text: '已过期',
                    value: _api.InvitationStatus.EXPIRED
                },
                {
                    text: '已取消',
                    value: _api.InvitationStatus.CANCELLED
                }
            ],
            onFilter: (value, record)=>record.status === value
        },
        {
            title: '邀请时间',
            dataIndex: 'invitedAt',
            key: 'invitedAt',
            render: (time)=>(0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                    title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                    children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 291,
                    columnNumber: 9
                }, this),
            sorter: (a, b)=>(0, _dayjs.default)(a.invitedAt).unix() - (0, _dayjs.default)(b.invitedAt).unix()
        },
        {
            title: '过期时间',
            dataIndex: 'expiresAt',
            key: 'expiresAt',
            render: (time, record)=>{
                const isExpired = record.isExpired;
                return (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                    title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                    children: (0, _jsxdevruntime.jsxDEV)(Text, {
                        type: isExpired ? 'danger' : 'secondary',
                        children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                        lineNumber: 305,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 304,
                    columnNumber: 11
                }, this);
            }
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record)=>{
                if (record.status === _api.InvitationStatus.PENDING && !record.isExpired) return (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                    title: "确定要取消这个邀请吗？",
                    onConfirm: ()=>handleCancelInvitation(record.id),
                    okText: "确定",
                    cancelText: "取消",
                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        size: "small",
                        danger: true,
                        icon: (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 324,
                            columnNumber: 49
                        }, void 0),
                        children: "取消邀请"
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                        lineNumber: 324,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 318,
                    columnNumber: 13
                }, this);
                return (0, _jsxdevruntime.jsxDEV)(Text, {
                    type: "secondary",
                    children: "-"
                }, void 0, false, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 330,
                    columnNumber: 16
                }, this);
            }
        }
    ];
    const filteredInvitations = invitations.filter((invitation)=>{
        const matchesSearch = !invitationSearchText || invitation.inviteeEmail.toLowerCase().includes(invitationSearchText.toLowerCase()) || invitation.inviteeName && invitation.inviteeName.toLowerCase().includes(invitationSearchText.toLowerCase());
        const matchesStatus = !statusFilter || invitation.status === statusFilter;
        return matchesSearch && matchesStatus;
    });
    return (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                style: {
                    marginBottom: 16
                },
                children: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    size: "large",
                    children: [
                        (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "团队成员"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 352,
                                    columnNumber: 13
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        fontSize: '24px',
                                        fontWeight: 'bold',
                                        color: '#1890ff'
                                    },
                                    children: (members || []).length
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 353,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 351,
                            columnNumber: 11
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "邀请记录"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 358,
                                    columnNumber: 13
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        fontSize: '24px',
                                        fontWeight: 'bold',
                                        color: '#52c41a'
                                    },
                                    children: (invitations || []).length
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 359,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 357,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 350,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                lineNumber: 349,
                columnNumber: 7
            }, this),
            (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                gutter: [
                    16,
                    16
                ],
                children: [
                    (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        lg: 12,
                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            title: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 373,
                                        columnNumber: 17
                                    }, void 0),
                                    (0, _jsxdevruntime.jsxDEV)("span", {
                                        children: "团队成员"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 374,
                                        columnNumber: 17
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 372,
                                columnNumber: 15
                            }, void 0),
                            children: [
                                (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: 16
                                    },
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        style: {
                                            width: '100%',
                                            justifyContent: 'space-between'
                                        },
                                        children: [
                                            (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                placeholder: "搜索成员姓名或邮箱",
                                                prefix: (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 383,
                                                    columnNumber: 27
                                                }, void 0),
                                                value: searchText,
                                                onChange: (e)=>setSearchText(e.target.value),
                                                style: {
                                                    width: '100%',
                                                    maxWidth: 250
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 381,
                                                columnNumber: 17
                                            }, this),
                                            selectedRowKeys.length > 0 && (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                                title: `确定要移除选中的 ${selectedRowKeys.length} 名成员吗？`,
                                                onConfirm: handleBatchRemove,
                                                okText: "确定",
                                                cancelText: "取消",
                                                children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    danger: true,
                                                    icon: (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 395,
                                                        columnNumber: 42
                                                    }, void 0),
                                                    size: "small",
                                                    children: [
                                                        "批量移除 (",
                                                        selectedRowKeys.length,
                                                        ")"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 395,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 389,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 380,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 379,
                                    columnNumber: 13
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                                    columns: columns,
                                    dataSource: filteredMembers,
                                    rowKey: "id",
                                    loading: loading,
                                    rowSelection: rowSelection,
                                    pagination: {
                                        showSizeChanger: true,
                                        showQuickJumper: true,
                                        showTotal: (total)=>`共 ${total} 名成员`,
                                        pageSize: 5,
                                        size: 'small'
                                    },
                                    size: "small"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 404,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 370,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                        lineNumber: 369,
                        columnNumber: 9
                    }, this),
                    (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                        xs: 24,
                        lg: 12,
                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            title: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 427,
                                        columnNumber: 17
                                    }, void 0),
                                    (0, _jsxdevruntime.jsxDEV)("span", {
                                        children: "邀请记录"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 428,
                                        columnNumber: 17
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 426,
                                columnNumber: 15
                            }, void 0),
                            children: [
                                (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: 16
                                    },
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        children: [
                                            (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                placeholder: "搜索邮箱",
                                                prefix: (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 437,
                                                    columnNumber: 27
                                                }, void 0),
                                                value: invitationSearchText,
                                                onChange: (e)=>setInvitationSearchText(e.target.value),
                                                style: {
                                                    width: 200
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 435,
                                                columnNumber: 17
                                            }, this),
                                            (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                                placeholder: "筛选状态",
                                                value: statusFilter,
                                                onChange: setStatusFilter,
                                                style: {
                                                    width: 120
                                                },
                                                allowClear: true,
                                                children: [
                                                    (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                        value: _api.InvitationStatus.PENDING,
                                                        children: "待确认"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 449,
                                                        columnNumber: 19
                                                    }, this),
                                                    (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                        value: _api.InvitationStatus.ACCEPTED,
                                                        children: "已接受"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 450,
                                                        columnNumber: 19
                                                    }, this),
                                                    (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                        value: _api.InvitationStatus.REJECTED,
                                                        children: "已拒绝"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 451,
                                                        columnNumber: 19
                                                    }, this),
                                                    (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                        value: _api.InvitationStatus.EXPIRED,
                                                        children: "已过期"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 452,
                                                        columnNumber: 19
                                                    }, this),
                                                    (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                        value: _api.InvitationStatus.CANCELLED,
                                                        children: "已取消"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 453,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 442,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 434,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 433,
                                    columnNumber: 13
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                                    columns: invitationColumns,
                                    dataSource: filteredInvitations,
                                    rowKey: "id",
                                    loading: invitationLoading,
                                    pagination: {
                                        showSizeChanger: true,
                                        showQuickJumper: true,
                                        showTotal: (total)=>`共 ${total} 条邀请记录`,
                                        pageSize: 5,
                                        size: 'small'
                                    },
                                    size: "small"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 459,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 424,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                        lineNumber: 423,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                lineNumber: 367,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
        lineNumber: 347,
        columnNumber: 5
    }, this);
};
_s(TeamMemberManagement, "ibv9zSlh18CONDw4Qd1cIedeMA0=");
_c = TeamMemberManagement;
var _default = TeamMemberManagement;
var _c;
$RefreshReg$(_c, "TeamMemberManagement");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/team-management/index.tsx": function (module, exports, __mako_require__){
/**
 * 集成团队管理页面
 *
 * 功能特性：
 * - 统一的团队管理界面，包含多个功能模块
 * - 团队名称卡片显示，提升用户体验
 * - 合并的成员与邀请管理功能
 * - 团队设置功能
 * - 权限控制，只有团队创建者可以访问管理功能
 *
 * 模块组织：
 * - 团队成员与邀请管理：查看、添加、移除团队成员，管理邀请
 * - 团队设置：编辑团队信息、删除团队
 */ "use strict";
var interop = __mako_require__("@swc/helpers/_/_interop_require_wildcard")._;
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _TeamMemberManagement = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/team-management/components/TeamMemberManagement.tsx"));
var _team = __mako_require__("src/services/team.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text, Paragraph } = _antd.Typography;
const { TextArea } = _antd.Input;
const TeamManagementPage = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [teamDetail, setTeamDetail] = (0, _react.useState)(null);
    // 团队编辑相关状态
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [updating, setUpdating] = (0, _react.useState)(false);
    const [form] = _antd.Form.useForm();
    // 团队删除相关状态
    const [deleteModalVisible, setDeleteModalVisible] = (0, _react.useState)(false);
    const [deleteConfirmText, setDeleteConfirmText] = (0, _react.useState)('');
    const [deleting, setDeleting] = (0, _react.useState)(false);
    // 邀请成员相关状态
    const [inviteModalVisible, setInviteModalVisible] = (0, _react.useState)(false);
    const [inviteForm] = _antd.Form.useForm();
    (0, _react.useEffect)(()=>{
        fetchTeamDetail();
    }, []);
    const fetchTeamDetail = async ()=>{
        try {
            setLoading(true);
            const detail = await _team.TeamService.getCurrentTeamDetail();
            setTeamDetail(detail);
        } catch (error) {
            console.error('获取团队详情失败:', error);
            // 如果获取失败，可能是没有选择团队，跳转到个人中心页面
            _max.history.push('/personal-center');
        } finally{
            setLoading(false);
        }
    };
    // 保存团队信息
    const handleSaveTeam = async (values)=>{
        try {
            setUpdating(true);
            await _team.TeamService.updateCurrentTeam(values);
            _antd.message.success('团队信息更新成功');
            setEditModalVisible(false);
            fetchTeamDetail();
        } catch (error) {
            console.error('更新团队失败:', error);
            _antd.message.error('更新团队失败');
        } finally{
            setUpdating(false);
        }
    };
    // 删除团队
    const handleDeleteTeam = async ()=>{
        if (deleteConfirmText !== (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.name)) {
            _antd.message.error('请输入正确的团队名称');
            return;
        }
        try {
            setDeleting(true);
            await _team.TeamService.deleteCurrentTeam();
            _antd.message.success('团队已删除');
            setDeleteModalVisible(false);
            // 删除成功后跳转到团队选择页面
            _max.history.push('/user/team-select');
        } catch (error) {
            console.error('删除团队失败:', error);
            _antd.message.error('删除团队失败');
        } finally{
            setDeleting(false);
        }
    };
    // 邀请新成员
    const handleInviteMembers = async (values)=>{
        try {
            const emailList = values.emails.split('\n').map((email)=>email.trim()).filter((email)=>email);
            // 使用邀请服务发送邀请
            const { InvitationService } = await __mako_require__.ensure2("src/services/invitation.ts").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/services/invitation.ts")));
            await InvitationService.sendInvitations({
                emails: emailList,
                message: values.message
            });
            setInviteModalVisible(false);
            inviteForm.resetFields();
            fetchTeamDetail(); // 刷新团队详情
            _antd.message.success('邀请发送成功');
        } catch (error) {
            console.error('邀请发送失败:', error);
            _antd.message.error('邀请发送失败');
        }
    };
    // 权限检查：只有团队创建者可以访问管理功能
    const hasManagePermission = (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.isCreator) || false;
    if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                textAlign: 'center',
                padding: '50px 0'
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                    size: "large"
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 172,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        marginTop: 16
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                        type: "secondary",
                        children: "正在加载团队信息..."
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 174,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 173,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/team-management/index.tsx",
            lineNumber: 171,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team-management/index.tsx",
        lineNumber: 170,
        columnNumber: 7
    }, this);
    if (!teamDetail) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    textAlign: 'center',
                    padding: '50px 0'
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.InfoCircleOutlined, {
                        style: {
                            fontSize: 48,
                            color: '#faad14',
                            marginBottom: 16
                        }
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 186,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                        level: 4,
                        children: "未找到团队信息"
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 187,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                        type: "secondary",
                        children: "请先选择一个团队"
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 188,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            marginTop: 16
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            onClick: ()=>_max.history.push('/personal-center'),
                            children: "返回个人中心"
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 190,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 189,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 185,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "src/pages/team-management/index.tsx",
            lineNumber: 184,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team-management/index.tsx",
        lineNumber: 183,
        columnNumber: 7
    }, this);
    // 权限不足提示
    if (!hasManagePermission) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                message: "权限不足",
                description: "只有团队创建者可以访问团队管理功能。如果您需要管理权限，请联系团队创建者。",
                type: "warning",
                showIcon: true,
                action: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    size: "small",
                    onClick: ()=>_max.history.push('/dashboard'),
                    children: "返回首页"
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 211,
                    columnNumber: 15
                }, void 0)
            }, void 0, false, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 205,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "src/pages/team-management/index.tsx",
            lineNumber: 204,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "src/pages/team-management/index.tsx",
        lineNumber: 203,
        columnNumber: 7
    }, this);
    // 操作菜单项 - 移除编辑和删除，添加邀请成员
    const menuItems = [
        {
            key: 'invite',
            label: '邀请成员',
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 228,
                columnNumber: 13
            }, this),
            onClick: ()=>setInviteModalVisible(true)
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                style: {
                    marginBottom: 16
                },
                extra: hasManagePermission && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                    menu: {
                        items: menuItems
                    },
                    placement: "bottomRight",
                    trigger: [
                        'click'
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "text",
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 247,
                            columnNumber: 23
                        }, void 0),
                        style: {
                            fontSize: 16
                        }
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 245,
                        columnNumber: 15
                    }, void 0)
                }, void 0, false, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 240,
                    columnNumber: 13
                }, void 0),
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    size: "large",
                    align: "center",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                            size: 48,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 257,
                                columnNumber: 19
                            }, void 0),
                            style: {
                                backgroundColor: '#1890ff',
                                fontSize: 20
                            }
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 255,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 3,
                                    style: {
                                        margin: 0,
                                        marginBottom: 4
                                    },
                                    children: teamDetail.name
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 264,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    children: [
                                        teamDetail.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/index.tsx",
                                                lineNumber: 270,
                                                columnNumber: 25
                                            }, void 0),
                                            color: "gold",
                                            children: "管理员"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/index.tsx",
                                            lineNumber: 269,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: teamDetail.description || '这个团队还没有描述'
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/index.tsx",
                                            lineNumber: 276,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 267,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 263,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 254,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 236,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamMemberManagement.default, {
                teamDetail: teamDetail,
                onRefresh: fetchTeamDetail
            }, void 0, false, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 285,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "编辑团队信息",
                open: editModalVisible,
                onCancel: ()=>{
                    setEditModalVisible(false);
                    form.resetFields();
                },
                footer: null,
                width: 600,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: form,
                    layout: "vertical",
                    onFinish: handleSaveTeam,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "name",
                            label: "团队名称",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称'
                                },
                                {
                                    min: 2,
                                    max: 50,
                                    message: '团队名称长度应在2-50个字符之间'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 314,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 306,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "description",
                            label: "团队描述",
                            rules: [
                                {
                                    max: 200,
                                    message: '团队描述不能超过200个字符'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 4,
                                placeholder: "请输入团队描述（可选）",
                                showCount: true,
                                maxLength: 200
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 323,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 316,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            style: {
                                marginBottom: 0,
                                textAlign: 'right'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>{
                                            setEditModalVisible(false);
                                            form.resetFields();
                                        },
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/index.tsx",
                                        lineNumber: 332,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        loading: updating,
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SaveOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team-management/index.tsx",
                                            lineNumber: 342,
                                            columnNumber: 23
                                        }, void 0),
                                        children: "保存"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/index.tsx",
                                        lineNumber: 338,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 331,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 330,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 301,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 291,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                            style: {
                                color: '#ff4d4f'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 355,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "danger",
                            children: "删除团队确认"
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 356,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 354,
                    columnNumber: 11
                }, void 0),
                open: deleteModalVisible,
                onCancel: ()=>{
                    setDeleteModalVisible(false);
                    setDeleteConfirmText('');
                },
                footer: null,
                width: 600,
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "警告：此操作不可恢复",
                        description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                                    children: "删除团队将会："
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 371,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ul", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "永久删除团队及所有相关数据"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/index.tsx",
                                            lineNumber: 373,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "移除所有团队成员"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/index.tsx",
                                            lineNumber: 374,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "清除团队设置和配置"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/index.tsx",
                                            lineNumber: 375,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                            children: "无法恢复任何数据"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/index.tsx",
                                            lineNumber: 376,
                                            columnNumber: 17
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 372,
                                    columnNumber: 15
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 370,
                            columnNumber: 13
                        }, void 0),
                        type: "error",
                        showIcon: true,
                        style: {
                            marginBottom: 24
                        }
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 367,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            marginBottom: 16
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            strong: true,
                            children: [
                                '请输入团队名称 "',
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    code: true,
                                    children: teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.name
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 387,
                                    columnNumber: 22
                                }, this),
                                '" 来确认删除：'
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 386,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 385,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        placeholder: `请输入：${teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.name}`,
                        value: deleteConfirmText,
                        onChange: (e)=>setDeleteConfirmText(e.target.value),
                        style: {
                            marginBottom: 24
                        }
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 391,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'right'
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    onClick: ()=>{
                                        setDeleteModalVisible(false);
                                        setDeleteConfirmText('');
                                    },
                                    children: "取消"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 400,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    danger: true,
                                    loading: deleting,
                                    disabled: deleteConfirmText !== (teamDetail === null || teamDetail === void 0 ? void 0 : teamDetail.name),
                                    onClick: handleDeleteTeam,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/index.tsx",
                                        lineNumber: 414,
                                        columnNumber: 21
                                    }, void 0),
                                    children: "确认删除团队"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/index.tsx",
                                    lineNumber: 408,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 399,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/team-management/index.tsx",
                        lineNumber: 398,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 352,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "邀请新成员",
                open: inviteModalVisible,
                onCancel: ()=>{
                    setInviteModalVisible(false);
                    inviteForm.resetFields();
                },
                footer: null,
                width: 600,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: inviteForm,
                    layout: "vertical",
                    onFinish: handleInviteMembers,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "emails",
                            label: "邮箱地址",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入邮箱地址'
                                }
                            ],
                            extra: "每行一个邮箱地址，支持批量邀请",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                                rows: 6,
                                placeholder: "请输入邮箱地址，每行一个 例如： <EMAIL> <EMAIL>"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 446,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 438,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "message",
                            label: "邀请消息（可选）",
                            extra: "您可以添加一些邀请消息，让被邀请人更好地了解邀请意图",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                                rows: 3,
                                placeholder: "欢迎加入我们的团队！我们期待与您一起工作...",
                                maxLength: 500,
                                showCount: true
                            }, void 0, false, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 456,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 451,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team-management/index.tsx",
                                            lineNumber: 465,
                                            columnNumber: 62
                                        }, void 0),
                                        children: "发送邀请"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/index.tsx",
                                        lineNumber: 465,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>setInviteModalVisible(false),
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/index.tsx",
                                        lineNumber: 468,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/index.tsx",
                                lineNumber: 464,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/index.tsx",
                            lineNumber: 463,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/index.tsx",
                    lineNumber: 433,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/team-management/index.tsx",
                lineNumber: 423,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/team-management/index.tsx",
        lineNumber: 234,
        columnNumber: 5
    }, this);
};
_s(TeamManagementPage, "6703RwhKaKQe4VRIBVfuV/YuZDY=", false, function() {
    return [
        _antd.Form.useForm,
        _antd.Form.useForm
    ];
});
_c = TeamManagementPage;
var _default = TeamManagementPage;
var _c;
$RefreshReg$(_c, "TeamManagementPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_team-management_index_tsx-async.js.map